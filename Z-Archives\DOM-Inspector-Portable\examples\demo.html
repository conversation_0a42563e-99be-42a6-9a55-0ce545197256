<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM Inspector Ultra v2.1 - Démonstration</title>
    
    <!-- DOM Inspector CSS -->
    <link rel="stylesheet" href="../css/dom-inspector-tooltip.css">
    
    <!-- Styles de démonstration -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(45deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .demo-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .demo-header p {
            margin: 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .demo-content {
            padding: 40px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            background: #f9fafb;
        }
        
        .demo-section h2 {
            color: #1f2937;
            margin-top: 0;
            font-size: 1.8em;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 10px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #10b981;
            transition: transform 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-card h3 {
            margin-top: 0;
            color: #059669;
        }
        
        .demo-button {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }
        
        .demo-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .demo-input:focus {
            outline: none;
            border-color: #3b82f6;
        }
        
        .demo-navigation {
            background: #1f2937;
            padding: 15px 0;
            margin-bottom: 30px;
        }
        
        .demo-nav-list {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            gap: 30px;
        }
        
        .demo-nav-item a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        
        .demo-nav-item a:hover {
            background-color: #374151;
        }
        
        .demo-footer {
            background: #111827;
            color: white;
            text-align: center;
            padding: 30px;
            margin-top: 40px;
        }
        
        .instructions {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            color: #92400e;
            margin-top: 0;
        }
        
        .shortcut-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .shortcut-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #3b82f6;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body x-data="domInspector">
    
    <div class="demo-container">
        <!-- STRATEGIC-TAG:header|demo-header|45 -->
        <header class="demo-header">
            <h1>🔍 DOM Inspector Ultra v2.1</h1>
            <p>Démonstration Interactive - Survolez n'importe quel élément !</p>
        </header>
        
        <!-- Instructions d'utilisation -->
        <div class="demo-content">
            <div class="instructions">
                <h3>📋 Instructions d'Utilisation</h3>
                <p><strong>1.</strong> Cliquez sur le bouton "🔍 Inspecteur DOM" en bas à droite</p>
                <p><strong>2.</strong> Survolez n'importe quel élément de cette page</p>
                <p><strong>3.</strong> Utilisez les raccourcis clavier pour plus d'efficacité</p>
                
                <div class="shortcut-list">
                    <div class="shortcut-item"><strong>CTRL+C</strong> - Copier rapport</div>
                    <div class="shortcut-item"><strong>CTRL+Q</strong> - Verrouiller tooltip</div>
                    <div class="shortcut-item"><strong>CTRL+SHIFT+C</strong> - Export JSON</div>
                    <div class="shortcut-item"><strong>CTRL+ALT+S</strong> - Balise stratégique</div>
                    <div class="shortcut-item"><strong>ÉCHAP</strong> - Désactiver</div>
                </div>
            </div>
            
            <!-- STRATEGIC-TAG:navigation|demo-navigation|85 -->
            <nav class="demo-navigation">
                <ul class="demo-nav-list">
                    <li class="demo-nav-item"><a href="#accueil">Accueil</a></li>
                    <li class="demo-nav-item"><a href="#services">Services</a></li>
                    <li class="demo-nav-item"><a href="#portfolio">Portfolio</a></li>
                    <li class="demo-nav-item"><a href="#contact">Contact</a></li>
                </ul>
            </nav>
            
            <!-- STRATEGIC-TAG:content|demo-sections|95 -->
            <section class="demo-section">
                <h2>🎯 Éléments Interactifs</h2>
                <p>Survolez ces éléments pour voir le DOM Inspector en action :</p>
                
                <div class="demo-grid">
                    <div class="demo-card">
                        <h3>Boutons</h3>
                        <button class="demo-button">Bouton Principal</button>
                        <button class="demo-button" style="background: linear-gradient(45deg, #ef4444, #dc2626);">Bouton Secondaire</button>
                    </div>
                    
                    <div class="demo-card">
                        <h3>Formulaire</h3>
                        <div class="demo-form">
                            <input type="text" class="demo-input" placeholder="Votre nom">
                            <input type="email" class="demo-input" placeholder="Votre email">
                            <button class="demo-button">Envoyer</button>
                        </div>
                    </div>
                    
                    <div class="demo-card">
                        <h3>Contenu</h3>
                        <p>Ce paragraphe contient du texte de démonstration pour tester l'inspection DOM.</p>
                        <ul>
                            <li>Élément de liste 1</li>
                            <li>Élément de liste 2</li>
                            <li>Élément de liste 3</li>
                        </ul>
                    </div>
                </div>
            </section>
            
            <!-- STRATEGIC-TAG:features|demo-features|130 -->
            <section class="demo-section">
                <h2>✨ Fonctionnalités Testables</h2>
                
                <div class="demo-grid">
                    <div class="demo-card">
                        <h3>🏷️ Balises Stratégiques</h3>
                        <p>Cette section contient des balises stratégiques pour une localisation précise.</p>
                    </div>
                    
                    <div class="demo-card">
                        <h3>🎨 Styles CSS</h3>
                        <p>Détection automatique des classes CSS et des styles appliqués.</p>
                    </div>
                    
                    <div class="demo-card">
                        <h3>🔗 Interactions</h3>
                        <p>Analyse des événements et des interactions JavaScript.</p>
                    </div>
                    
                    <div class="demo-card">
                        <h3>📊 Métadonnées</h3>
                        <p>Informations complètes sur la structure DOM et les dépendances.</p>
                    </div>
                </div>
            </section>
        </div>
        
        <!-- STRATEGIC-TAG:footer|demo-footer|165 -->
        <footer class="demo-footer">
            <p>🚀 DOM Inspector Ultra v2.1 - Développé par Cisco-FlexoDiv</p>
            <p>L'outil le plus précis au monde pour l'inspection DOM !</p>
        </footer>
    </div>
    
    <!-- Alpine.js -->
    <script src="../dependencies/alpinejs/alpine.min.js" defer></script>
    
    <!-- DOM Inspector Script -->
    <script src="../js/dom-inspector-tooltip.js"></script>
    
</body>
</html>
