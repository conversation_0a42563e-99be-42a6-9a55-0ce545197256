import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Plant } from '@/types';
import { CalendarEvent, CreateCalendarEventData, TreatmentType } from '@/types/calendar';
import { getPlants, addCalendarEvent, updateCalendarEvent } from '@/services/api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  event?: CalendarEvent | null;
  onSave: () => void;
}

/**
 * Modal pour créer ou modifier un événement de calendrier
 */
const EventModal: React.FC<EventModalProps> = ({ isOpen, onClose, event, onSave }) => {
  const { user } = useAuth();
  const [plants, setPlants] = useState<Plant[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    plantId: '',
    plantName: '',
    treatmentType: 'traitement' as TreatmentType,
    title: '',
    description: '',
    startDate: '',
    startTime: '',
    duration: 60, // en minutes
    isRecurring: false,
    recurrenceFrequency: 'daily' as 'daily' | 'weekly' | 'monthly',
    recurrenceInterval: 1,
    recurrenceEndDate: ''
  });

  // Chargement des plantes
  useEffect(() => {
    if (!user || !isOpen) return;

    const unsubscribe = getPlants(user.uid, (fetchedPlants) => {
      setPlants(fetchedPlants);
    });

    return () => unsubscribe();
  }, [user, isOpen]);

  // Initialisation du formulaire avec les données de l'événement existant
  useEffect(() => {
    if (event) {
      const startDate = new Date(event.startDate);
      setFormData({
        plantId: event.plantId,
        plantName: event.plantName,
        treatmentType: event.treatmentType,
        title: event.title,
        description: event.description,
        startDate: startDate.toISOString().split('T')[0],
        startTime: startDate.toTimeString().slice(0, 5),
        duration: Math.round((event.endDate.getTime() - event.startDate.getTime()) / (1000 * 60)),
        isRecurring: event.isRecurring,
        recurrenceFrequency: event.recurrencePattern?.frequency || 'daily',
        recurrenceInterval: event.recurrencePattern?.interval || 1,
        recurrenceEndDate: event.recurrencePattern?.endDate?.toISOString().split('T')[0] || ''
      });
    } else {
      // Réinitialiser le formulaire pour un nouvel événement
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      setFormData({
        plantId: '',
        plantName: '',
        treatmentType: 'traitement',
        title: '',
        description: '',
        startDate: tomorrow.toISOString().split('T')[0],
        startTime: '09:00',
        duration: 60,
        isRecurring: false,
        recurrenceFrequency: 'daily',
        recurrenceInterval: 1,
        recurrenceEndDate: ''
      });
    }
  }, [event, isOpen]);

  // Mise à jour du nom de la plante quand la plante sélectionnée change
  useEffect(() => {
    if (formData.plantId) {
      const selectedPlant = plants.find(p => p.id === formData.plantId);
      if (selectedPlant) {
        setFormData(prev => ({ ...prev, plantName: selectedPlant.name }));
      }
    }
  }, [formData.plantId, plants]);

  // Gestion de la soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || isLoading) return;

    setIsLoading(true);

    try {
      // Construction des dates
      const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`);
      const endDateTime = new Date(startDateTime.getTime() + formData.duration * 60 * 1000);

      if (event) {
        // Mise à jour d'un événement existant
        await updateCalendarEvent(user.uid, event.id, {
          plantName: formData.plantName,
          treatmentType: formData.treatmentType,
          title: formData.title,
          description: formData.description,
          startDate: startDateTime,
          endDate: endDateTime,
          isRecurring: formData.isRecurring,
          recurrencePattern: formData.isRecurring ? {
            frequency: formData.recurrenceFrequency,
            interval: formData.recurrenceInterval,
            endDate: formData.recurrenceEndDate ? new Date(formData.recurrenceEndDate) : undefined
          } : undefined
        });
      } else {
        // Création d'un nouvel événement
        const eventData: CreateCalendarEventData = {
          plantId: formData.plantId,
          plantName: formData.plantName,
          treatmentType: formData.treatmentType,
          title: formData.title,
          description: formData.description,
          startDate: startDateTime,
          endDate: endDateTime,
          isRecurring: formData.isRecurring,
          recurrencePattern: formData.isRecurring ? {
            frequency: formData.recurrenceFrequency,
            interval: formData.recurrenceInterval,
            endDate: formData.recurrenceEndDate ? new Date(formData.recurrenceEndDate) : undefined
          } : undefined,
          createdBy: 'user'
        };

        await addCalendarEvent(user.uid, eventData);
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'événement:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>
            {event ? 'Modifier l\'événement' : 'Nouvel événement de traitement'}
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Sélection de la plante */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Plante *
              </label>
              <select
                value={formData.plantId}
                onChange={(e) => setFormData(prev => ({ ...prev, plantId: e.target.value }))}
                required
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Sélectionner une plante</option>
                {plants.map((plant) => (
                  <option key={plant.id} value={plant.id}>
                    {plant.name} {plant.species && `(${plant.species})`}
                  </option>
                ))}
              </select>
            </div>

            {/* Type de traitement */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type de traitement *
              </label>
              <select
                value={formData.treatmentType}
                onChange={(e) => setFormData(prev => ({ ...prev, treatmentType: e.target.value as TreatmentType }))}
                required
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="traitement">Traitement</option>
                <option value="fertilisation">Fertilisation</option>
                <option value="arrosage">Arrosage</option>
                <option value="rempotage">Rempotage</option>
                <option value="taille">Taille</option>
              </select>
            </div>

            {/* Titre */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Titre *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                required
                placeholder="Ex: Traitement anti-pucerons"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                placeholder="Détails du traitement à effectuer..."
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Date et heure */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date *
                </label>
                <input
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                  required
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Heure *
                </label>
                <input
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                  required
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Durée */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Durée (minutes)
              </label>
              <input
                type="number"
                value={formData.duration}
                onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 60 }))}
                min="15"
                max="480"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Récurrence */}
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.isRecurring}
                  onChange={(e) => setFormData(prev => ({ ...prev, isRecurring: e.target.checked }))}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="text-sm font-medium text-gray-700">Événement récurrent</span>
              </label>
            </div>

            {/* Options de récurrence */}
            {formData.isRecurring && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-md">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fréquence
                    </label>
                    <select
                      value={formData.recurrenceFrequency}
                      onChange={(e) => setFormData(prev => ({ ...prev, recurrenceFrequency: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="daily">Quotidienne</option>
                      <option value="weekly">Hebdomadaire</option>
                      <option value="monthly">Mensuelle</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Intervalle
                    </label>
                    <input
                      type="number"
                      value={formData.recurrenceInterval}
                      onChange={(e) => setFormData(prev => ({ ...prev, recurrenceInterval: parseInt(e.target.value) || 1 }))}
                      min="1"
                      max="30"
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date de fin (optionnelle)
                  </label>
                  <input
                    type="date"
                    value={formData.recurrenceEndDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, recurrenceEndDate: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
              </div>
            )}

            {/* Boutons d'action */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? 'Sauvegarde...' : (event ? 'Modifier' : 'Créer')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EventModal;
