# Script PowerShell pour synchroniser master vers main
# Usage: .\scripts\sync-branches.ps1

Write-Host "🔄 Synchronisation des branches master -> main" -ForegroundColor Cyan

# Vérifier qu'on est sur master
$currentBranch = git branch --show-current
if ($currentBranch -ne "master") {
    Write-Host "❌ Erreur: Vous devez être sur la branche master" -ForegroundColor Red
    Write-Host "📍 Branche actuelle: $currentBranch" -ForegroundColor Yellow
    Write-Host "💡 Exécutez: git checkout master" -ForegroundColor Green
    exit 1
}

# Vérifier que le working tree est propre
$status = git status --porcelain
if ($status) {
    Write-Host "❌ Erreur: Des modifications non commitées détectées" -ForegroundColor Red
    Write-Host "💡 Commitez vos modifications avant de synchroniser" -ForegroundColor Green
    git status
    exit 1
}

# Pousser master vers origin/master
Write-Host "📤 Push de master vers origin/master..." -ForegroundColor Yellow
git push origin master

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Erreur lors du push de master" -ForegroundColor Red
    exit 1
}

# Synchroniser master vers main
Write-Host "🔄 Synchronisation master -> main..." -ForegroundColor Yellow
git push origin master:main

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Erreur lors de la synchronisation vers main" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Synchronisation réussie!" -ForegroundColor Green
Write-Host "🚀 Netlify va déployer automatiquement depuis main" -ForegroundColor Cyan
Write-Host "🌐 Vérifiez le déploiement sur: https://app.netlify.com" -ForegroundColor Blue
