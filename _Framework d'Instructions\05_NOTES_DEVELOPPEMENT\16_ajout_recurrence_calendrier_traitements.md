# Ajout de la Récurrence au Calendrier des Traitements

## 📋 Problème Identifié

**Date**: 26 juillet 2025  
**Rapporté par**: Cisco  
**Problème**: Incohérence dans le calendrier des traitements - l'option "rappel" existe mais aucune fonctionnalité de récurrence n'est disponible pour configurer des rappels automatiques (quotidiens, hebdomadaires, mensuels).

## 🔍 Analyse du Problème

### Situation Initiale
- Le calendrier des traitements propose une option "rappel" dans les types d'événements
- Aucune interface utilisateur pour configurer la récurrence
- Aucune logique backend pour gérer les événements récurrents
- Incohérence entre l'interface et les fonctionnalités disponibles

### Systèmes de Calendrier Identifiés
1. **Système de calendrier classique** (`CalendarEvent`) - avec récurrence complète
2. **Système d'événements de diagnostic** (`DiagnosticEvent`) - sans récurrence

## 🛠️ Solution Implémentée

### 1. Mise à Jour des Types TypeScript

**Fichier**: `src/types/notifications.ts`

#### A. Import des Types de Récurrence
```typescript
import { TreatmentType, RecurrencePattern } from './calendar';
```

#### B. Ajout des Champs de Récurrence à `DiagnosticEvent`
```typescript
export interface DiagnosticEvent {
  // ... champs existants
  /** Indique si l'événement est récurrent */
  isRecurring?: boolean;
  /** Pattern de récurrence (si applicable) */
  recurrencePattern?: RecurrencePattern;
}
```

#### C. Ajout des Champs de Récurrence à `CreateDiagnosticEventData`
```typescript
export interface CreateDiagnosticEventData {
  // ... champs existants
  isRecurring?: boolean;
  recurrencePattern?: RecurrencePattern;
}
```

#### D. Ajout des Champs de Récurrence à `UpdateDiagnosticEventData`
```typescript
export interface UpdateDiagnosticEventData {
  // ... champs existants
  isRecurring?: boolean;
  recurrencePattern?: RecurrencePattern;
}
```

### 2. Mise à Jour de l'Interface Utilisateur

**Fichier**: `src/components/features/Calendar/CalendarView.tsx`

#### A. Ajout des Champs au State du Formulaire
```typescript
const [formData, setFormData] = useState({
  // ... champs existants
  isRecurring: false,
  recurrenceFrequency: 'weekly' as 'daily' | 'weekly' | 'monthly',
  recurrenceInterval: 1,
  recurrenceEndDate: ''
});
```

#### B. Interface de Récurrence dans CreateEventModal
- Checkbox "Événement récurrent (rappel automatique)"
- Sélecteur de fréquence (Quotidienne, Hebdomadaire, Mensuelle)
- Champ d'intervalle (ex: tous les 2 jours)
- Date de fin optionnelle

#### C. Interface de Récurrence dans EditEventModal
- Même interface que pour la création
- Initialisation avec les valeurs existantes de l'événement

#### D. Logique de Soumission des Formulaires
```typescript
// Création d'événement
await onCreateEvent({
  // ... données existantes
  isRecurring: formData.isRecurring,
  recurrencePattern: formData.isRecurring ? {
    frequency: formData.recurrenceFrequency,
    interval: formData.recurrenceInterval,
    endDate: formData.recurrenceEndDate ? new Date(formData.recurrenceEndDate) : undefined
  } : undefined
});

// Mise à jour d'événement
await onUpdateEvent({
  // ... données existantes
  isRecurring: formData.isRecurring,
  recurrencePattern: formData.isRecurring ? {
    frequency: formData.recurrenceFrequency,
    interval: formData.recurrenceInterval,
    endDate: formData.recurrenceEndDate ? new Date(formData.recurrenceEndDate) : undefined
  } : undefined
});
```

### 3. Compatibilité Backend

**Fichier**: `src/services/notificationService.ts`

Les fonctions `createDiagnosticEvent` et `updateDiagnosticEvent` utilisent déjà le spread operator (`...eventData`), donc les nouveaux champs de récurrence sont automatiquement inclus sans modification supplémentaire.

## 🎯 Fonctionnalités Ajoutées

### Interface Utilisateur
1. **Checkbox de récurrence** : Active/désactive la récurrence
2. **Sélecteur de fréquence** : Quotidienne, Hebdomadaire, Mensuelle
3. **Champ d'intervalle** : Permet de spécifier "tous les X jours/semaines/mois"
4. **Date de fin optionnelle** : Limite la récurrence dans le temps
5. **Interface cohérente** : Même design dans création et modification

### Logique Backend
1. **Stockage des patterns de récurrence** : Sauvegarde automatique des paramètres
2. **Compatibilité ascendante** : Les événements existants restent fonctionnels
3. **Validation des données** : Types TypeScript stricts pour la récurrence

## 📝 Prochaines Étapes Recommandées

### 1. Génération Automatique d'Événements Récurrents
- Créer une fonction pour générer les occurrences futures
- Implémenter un système de tâches planifiées
- Gérer la suppression/modification des séries récurrentes

### 2. Interface Utilisateur Avancée
- Aperçu des prochaines occurrences
- Gestion des exceptions (modifier une seule occurrence)
- Options de récurrence plus complexes (ex: tous les premiers lundis du mois)

### 3. Notifications Intelligentes
- Regroupement des notifications récurrentes
- Rappels adaptatifs selon l'historique utilisateur
- Intégration avec les recommandations Gemini

## ✅ Tests de Validation

### Tests à Effectuer
1. **Création d'événement récurrent** : Vérifier que les champs sont sauvegardés
2. **Modification d'événement récurrent** : Vérifier que les paramètres sont chargés et modifiables
3. **Événements non-récurrents** : Vérifier que la fonctionnalité existante n'est pas affectée
4. **Validation des formulaires** : Tester les cas limites (intervalles, dates)

### Cas de Test Spécifiques
- Événement quotidien avec intervalle de 2 jours
- Événement hebdomadaire avec date de fin
- Événement mensuel sans date de fin
- Modification d'un événement récurrent vers non-récurrent
- Modification d'un événement non-récurrent vers récurrent

## 🔧 Détails Techniques

### Types de Récurrence Supportés
- **daily** : Récurrence quotidienne
- **weekly** : Récurrence hebdomadaire  
- **monthly** : Récurrence mensuelle

### Structure RecurrencePattern
```typescript
interface RecurrencePattern {
  frequency: 'daily' | 'weekly' | 'monthly';
  interval: number; // 1-30
  endDate?: Date;
  maxOccurrences?: number; // Pour usage futur
}
```

### Compatibilité
- **Rétrocompatible** : Les événements existants continuent de fonctionner
- **Optionnel** : Les champs de récurrence sont optionnels
- **Extensible** : Structure prête pour des fonctionnalités avancées

---

**Status**: ✅ Implémenté - Interface utilisateur et types mis à jour  
**Prochaine étape**: Tests utilisateur et implémentation de la génération automatique d'événements
