# 🛠️ Guide de Dépannage - DOM Inspector Ultra v2.1

**Solutions aux problèmes courants et guide de diagnostic**

## 🚨 Problèmes Courants

### 1. <PERSON>n "Inspecteur DOM" N'Apparaît Pas

#### Symptômes
- Aucun bouton visible en bas à droite de la page
- Console sans erreurs apparentes

#### Causes Possibles
1. **Alpine.js non chargé** ou chargé après le script DOM Inspector
2. **Attribut `x-data` manquant** sur l'élément `<body>`
3. **Conflit avec d'autres scripts**
4. **CSS du bouton masqué** par d'autres styles

#### Solutions

**Solution 1 : Vérifier l'ordre de chargement**
```html
<!-- CORRECT : Alpine.js AVANT DOM Inspector -->
<script src="DOM-Inspector-Portable/dependencies/alpinejs/alpine.min.js" defer></script>
<script src="DOM-Inspector-Portable/js/dom-inspector-tooltip.js"></script>

<!-- INCORRECT : Ordre inversé -->
<script src="DOM-Inspector-Portable/js/dom-inspector-tooltip.js"></script>
<script src="DOM-Inspector-Portable/dependencies/alpinejs/alpine.min.js" defer></script>
```

**Solution 2 : Vérifier l'attribut x-data**
```html
<!-- CORRECT -->
<body x-data="domInspector">

<!-- INCORRECT -->
<body>
```

**Solution 3 : Diagnostic console**
```javascript
// Ouvrir la console et taper :
console.log('Alpine:', window.Alpine);
console.log('DOM Inspector:', window.domInspectorInstance);

// Si Alpine est undefined :
// - Vérifiez que le script Alpine.js se charge
// - Vérifiez l'ordre des scripts

// Si DOM Inspector est undefined :
// - Vérifiez que le script DOM Inspector se charge
// - Vérifiez qu'il n'y a pas d'erreurs JavaScript
```

**Solution 4 : Forcer l'affichage du bouton**
```css
/* Ajouter dans votre CSS pour débugger */
.dom-inspector-toggle {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 99999 !important;
  background: red !important; /* Pour le voir facilement */
}
```

### 2. Le Tooltip Ne S'Affiche Pas

#### Symptômes
- Le bouton fonctionne (change d'état)
- Aucun tooltip au survol des éléments
- Curseur en forme de croix visible

#### Solutions

**Solution 1 : Vérifier les styles CSS**
```css
/* Vérifier que le tooltip n'est pas masqué */
.dom-inspector-tooltip {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 99999 !important;
}
```

**Solution 2 : Diagnostic JavaScript**
```javascript
// Dans la console, activer l'inspecteur puis :
document.addEventListener('mousemove', (e) => {
  console.log('Mouse move:', e.target);
  console.log('Inspector active:', window.domInspectorInstance?.isActive);
});
```

**Solution 3 : Vérifier les conflits d'événements**
```javascript
// Vérifier si d'autres scripts interceptent les événements
document.addEventListener('mousemove', (e) => {
  console.log('Event listeners:', getEventListeners(e.target));
}, true);
```

### 3. Raccourcis Clavier Ne Fonctionnent Pas

#### Symptômes
- CTRL+C, CTRL+Q, etc. sans effet
- Aucune réaction aux raccourcis

#### Solutions

**Solution 1 : Vérifier les conflits**
```javascript
// Tester si les événements sont interceptés
document.addEventListener('keydown', (e) => {
  if (e.ctrlKey && e.key === 'c') {
    console.log('CTRL+C détecté');
    e.preventDefault();
  }
}, true); // true = capture phase
```

**Solution 2 : Désactiver autres gestionnaires**
```javascript
// Temporairement désactiver autres gestionnaires
document.addEventListener('keydown', (e) => {
  if (e.ctrlKey) {
    e.stopImmediatePropagation();
  }
}, true);
```

### 4. Erreurs Alpine.js

#### Symptômes
- Erreurs dans la console mentionnant Alpine
- `Cannot read property of undefined`

#### Solutions

**Solution 1 : Vérifier la version Alpine.js**
```javascript
// Dans la console :
console.log('Alpine version:', Alpine.version);
// Doit afficher "3.14.9" ou supérieur
```

**Solution 2 : Réinitialiser Alpine.js**
```javascript
// Si Alpine ne démarre pas :
if (window.Alpine && !Alpine.version) {
  Alpine.start();
}
```

**Solution 3 : Conflit avec d'autres frameworks**
```html
<!-- Si vous utilisez déjà Alpine.js -->
<script>
// Fusionner les données
document.addEventListener('alpine:init', () => {
  Alpine.data('myApp', () => ({
    ...Alpine.data('domInspector')(),
    // Vos données existantes
  }));
});
</script>
```

### 5. Problèmes de Performance

#### Symptômes
- Page lente après activation
- Lag au survol des éléments
- Navigateur qui rame

#### Solutions

**Solution 1 : Limiter la fréquence d'analyse**
```javascript
// Ajouter un throttle aux événements
let throttleTimeout;
document.addEventListener('mousemove', (e) => {
  if (throttleTimeout) return;
  throttleTimeout = setTimeout(() => {
    // Logique d'inspection
    throttleTimeout = null;
  }, 16); // ~60fps
});
```

**Solution 2 : Désactiver sur mobile**
```javascript
// Désactiver automatiquement sur mobile
if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
  console.log('DOM Inspector désactivé sur mobile');
  // Ne pas charger le DOM Inspector
}
```

## 🔍 Outils de Diagnostic

### Console de Debug

```javascript
// Activer le mode debug
window.DOMInspectorDebug = true;

// Informations système
console.log('=== DOM INSPECTOR DEBUG ===');
console.log('Alpine.js:', window.Alpine?.version || 'Non chargé');
console.log('DOM Inspector:', window.domInspectorInstance ? 'Chargé' : 'Non chargé');
console.log('Navigateur:', navigator.userAgent);
console.log('Viewport:', window.innerWidth + 'x' + window.innerHeight);

// Test des fonctionnalités
if (window.domInspectorInstance) {
  console.log('État actuel:', {
    isActive: window.domInspectorInstance.isActive,
    isLocked: window.domInspectorInstance.isTooltipLocked,
    currentElement: window.domInspectorInstance.currentElement
  });
}
```

### Test de Compatibilité

```javascript
// Vérifier la compatibilité du navigateur
function checkCompatibility() {
  const features = {
    'Proxy': typeof Proxy !== 'undefined',
    'MutationObserver': typeof MutationObserver !== 'undefined',
    'CustomEvent': typeof CustomEvent !== 'undefined',
    'addEventListener': typeof document.addEventListener === 'function',
    'querySelector': typeof document.querySelector === 'function',
    'classList': 'classList' in document.createElement('div'),
    'dataset': 'dataset' in document.createElement('div')
  };
  
  console.log('Compatibilité navigateur:', features);
  
  const incompatible = Object.entries(features)
    .filter(([key, value]) => !value)
    .map(([key]) => key);
    
  if (incompatible.length > 0) {
    console.warn('Fonctionnalités non supportées:', incompatible);
    return false;
  }
  
  return true;
}

checkCompatibility();
```

## 🚑 Solutions d'Urgence

### Réinitialisation Complète

```javascript
// Script de réinitialisation d'urgence
function emergencyReset() {
  // Supprimer tous les éléments DOM Inspector
  document.querySelectorAll('.dom-inspector-tooltip, .dom-inspector-toggle').forEach(el => el.remove());
  
  // Nettoyer les événements
  document.body.classList.remove('dom-inspector-active');
  
  // Réinitialiser les variables globales
  window.domInspectorInstance = null;
  
  // Recharger Alpine.js si nécessaire
  if (window.Alpine) {
    Alpine.start();
  }
  
  console.log('DOM Inspector réinitialisé');
}

// Utiliser en cas de problème grave
emergencyReset();
```

### Mode Sans Échec

```html
<!-- Version minimale pour tester -->
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="DOM-Inspector-Portable/css/dom-inspector-tooltip.css">
</head>
<body x-data="domInspector">
    <h1>Test Minimal</h1>
    <p>Si ce test fonctionne, le problème vient de votre intégration.</p>
    
    <script src="DOM-Inspector-Portable/dependencies/alpinejs/alpine.min.js" defer></script>
    <script src="DOM-Inspector-Portable/js/dom-inspector-tooltip.js"></script>
</body>
</html>
```

## 📋 Checklist de Diagnostic

### Avant de Demander de l'Aide

- [ ] **Fichiers présents** : Vérifiez que tous les fichiers sont copiés
- [ ] **Ordre des scripts** : Alpine.js avant DOM Inspector
- [ ] **Attribut x-data** : Présent sur l'élément `<body>`
- [ ] **Console propre** : Aucune erreur JavaScript
- [ ] **Navigateur supporté** : Chrome 63+, Firefox 67+, Safari 11.1+, Edge 79+
- [ ] **Test minimal** : Essayez avec l'exemple `demo.html`
- [ ] **Cache vidé** : Rechargez avec CTRL+F5
- [ ] **Mode incognito** : Testez sans extensions

### Informations à Fournir

Quand vous demandez de l'aide, incluez :

1. **Version du navigateur** : Chrome 91, Firefox 89, etc.
2. **Messages d'erreur** : Copie exacte de la console
3. **Code HTML** : Structure de votre page
4. **Étapes pour reproduire** : Ce que vous avez fait
5. **Comportement attendu vs réel** : Ce qui devrait se passer vs ce qui se passe

## 🔧 Outils Utiles

### Extensions Navigateur
- **Vue.js DevTools** : Pour débugger Alpine.js
- **React Developer Tools** : Si vous utilisez React
- **Web Developer** : Outils généraux de développement

### Commandes Console Utiles
```javascript
// Lister tous les éléments avec x-data
document.querySelectorAll('[x-data]');

// Vérifier les événements sur un élément
getEventListeners(document.body);

// Forcer l'activation
window.domInspectorInstance?.toggle();

// Analyser un élément spécifique
window.domInspectorInstance?.analyzeElement(document.querySelector('#myElement'));
```

## 📞 Support

Si aucune solution ne fonctionne :

1. **Testez avec `demo.html`** - Si ça marche, le problème vient de votre intégration
2. **Vérifiez les exemples** dans `examples/`
3. **Consultez l'API** dans `docs/API.md`
4. **Créez un test minimal** pour isoler le problème

---

**🛠️ DOM Inspector Ultra v2.1 - Diagnostic et Réparation !**
