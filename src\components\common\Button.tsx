
import React, { ReactNode } from 'react';
import { Spinner } from './Spinner';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: 'primary' | 'secondary';
  isLoading?: boolean;
  className?: string;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  isLoading = false,
  className = '',
  ...props
}) => {
  const baseStyles = 'px-6 py-3 font-semibold rounded-lg transition-transform duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#100f1c] disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105';

  const variantStyles = {
    primary: 'text-white bg-gradient-to-r from-[#d385f5] to-[#a364f7] focus:ring-[#d385f5]',
    secondary: 'text-white bg-[#1c1a31] border border-[#a364f7] focus:ring-[#a364f7]',
  };

  return (
    <button
      className={`${baseStyles} ${variantStyles[variant]} ${className}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading ? <Spinner size="sm" /> : children}
    </button>
  );
};
