# 📚 API Documentation - DOM Inspector Ultra v2.1

**Documentation complète de l'API et des méthodes disponibles**

## 🎯 Vue d'Ensemble

Le DOM Inspector Ultra expose une API complète via Alpine.js pour une intégration avancée et une personnalisation poussée.

## 🔧 Initialisation

### Méthode Standard
```html
<body x-data="domInspector">
```

### Méthode Avancée avec Données Personnalisées
```html
<body x-data="{ 
    ...domInspector, 
    customData: 'valeur',
    customMethod() { /* logique */ }
}">
```

## 📋 Propriétés Publiques

### État de l'Inspecteur

#### `isActive` (Boolean)
- **Description** : État d'activation de l'inspecteur
- **Valeur par défaut** : `false`
- **Lecture seule** : Non

```javascript
// Vérifier si l'inspecteur est actif
if (this.isActive) {
    console.log('Inspecteur activé');
}
```

#### `isTooltipLocked` (Boolean)
- **Description** : État de verrouillage du tooltip
- **Valeur par défaut** : `false`
- **Lecture seule** : Non

#### `currentElement` (HTMLElement|null)
- **Description** : Élément actuellement inspecté
- **Valeur par défaut** : `null`
- **Lecture seule** : Oui

### Éléments DOM

#### `tooltip` (HTMLElement|null)
- **Description** : Référence vers l'élément tooltip
- **Valeur par défaut** : `null`
- **Lecture seule** : Oui

## 🎮 Méthodes Publiques

### Contrôle Principal

#### `toggle()`
- **Description** : Active/désactive l'inspecteur
- **Paramètres** : Aucun
- **Retour** : `void`

```javascript
// Activer/désactiver l'inspecteur
this.toggle();
```

#### `activate()`
- **Description** : Active l'inspecteur
- **Paramètres** : Aucun
- **Retour** : `void`

#### `deactivate()`
- **Description** : Désactive l'inspecteur
- **Paramètres** : Aucun
- **Retour** : `void`

### Gestion du Tooltip

#### `showTooltip(element, event)`
- **Description** : Affiche le tooltip pour un élément
- **Paramètres** :
  - `element` (HTMLElement) : Élément à inspecter
  - `event` (MouseEvent) : Événement de souris
- **Retour** : `void`

#### `hideTooltip()`
- **Description** : Masque le tooltip
- **Paramètres** : Aucun
- **Retour** : `void`

#### `toggleTooltipLock()`
- **Description** : Verrouille/déverrouille le tooltip
- **Paramètres** : Aucun
- **Retour** : `void`

### Analyse et Export

#### `analyzeElement(element)`
- **Description** : Analyse complète d'un élément
- **Paramètres** :
  - `element` (HTMLElement) : Élément à analyser
- **Retour** : `Promise<Object>` - Données d'analyse

```javascript
// Analyser un élément
const analysis = await this.analyzeElement(document.querySelector('#myElement'));
console.log(analysis);
```

#### `copyElementInfo()`
- **Description** : Copie les informations de l'élément actuel
- **Paramètres** : Aucun
- **Retour** : `Promise<void>`

#### `exportElementJSON()`
- **Description** : Exporte les données en format JSON
- **Paramètres** : Aucun
- **Retour** : `Promise<void>`

#### `generateAndCopyStrategicTag()`
- **Description** : Génère et copie une balise stratégique
- **Paramètres** : Aucun
- **Retour** : `Promise<void>`

## 🔍 Méthodes d'Analyse

### Signature d'Élément

#### `createElementSignature(element)`
- **Description** : Crée une signature unique pour un élément
- **Paramètres** :
  - `element` (HTMLElement) : Élément à analyser
- **Retour** : `Object` - Signature de l'élément

```javascript
const signature = this.createElementSignature(element);
// Retourne: { id, classes, tagName, textContent, attributes }
```

### Détection des Interactions

#### `detectInteractions(element)`
- **Description** : Détecte les interactions sur un élément
- **Paramètres** :
  - `element` (HTMLElement) : Élément à analyser
- **Retour** : `Array<Object>` - Liste des interactions

### Balises Stratégiques

#### `detectStrategicTags(element)`
- **Description** : Détecte les balises stratégiques
- **Paramètres** :
  - `element` (HTMLElement) : Élément à analyser
- **Retour** : `Object` - Informations sur les balises

#### `generateStrategicTag(element, lineNumber)`
- **Description** : Génère une balise stratégique
- **Paramètres** :
  - `element` (HTMLElement) : Élément cible
  - `lineNumber` (String|Number) : Numéro de ligne estimé
- **Retour** : `Object` - Balise générée

## ⌨️ Gestion des Événements

### Raccourcis Clavier

#### `handleKeyDown(event)`
- **Description** : Gestionnaire des raccourcis clavier
- **Paramètres** :
  - `event` (KeyboardEvent) : Événement clavier
- **Retour** : `void`

**Raccourcis supportés** :
- `CTRL+C` : Copier les informations
- `CTRL+Q` : Verrouiller/déverrouiller
- `CTRL+SHIFT+C` : Export JSON
- `CTRL+ALT+S` : Balise stratégique
- `ÉCHAP` : Désactiver

### Événements de Souris

#### `handleMouseMove(event)`
- **Description** : Gestionnaire du mouvement de souris
- **Paramètres** :
  - `event` (MouseEvent) : Événement de souris
- **Retour** : `void`

#### `handleMouseLeave(event)`
- **Description** : Gestionnaire de sortie de souris
- **Paramètres** :
  - `event` (MouseEvent) : Événement de souris
- **Retour** : `void`

## 🎨 Personnalisation

### Variables CSS

```css
:root {
  --tooltip-bg: rgba(30, 41, 59, 0.95);
  --tooltip-border: #3b82f6;
  --tooltip-text: #f8fafc;
  --tooltip-accent: #10b981;
  --tooltip-warning: #f59e0b;
  --tooltip-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  --tooltip-radius: 8px;
  --tooltip-z-index: 10000;
}
```

### Méthodes de Personnalisation

#### `updateTooltipPosition(x, y)`
- **Description** : Met à jour la position du tooltip
- **Paramètres** :
  - `x` (Number) : Position X
  - `y` (Number) : Position Y
- **Retour** : `void`

## 📊 Format des Données

### Objet d'Analyse

```javascript
{
  // Informations de base
  tagName: "DIV",
  id: "myElement",
  classes: "container mx-auto",
  textContent: "Contenu de l'élément",
  
  // Position et contexte
  htmlFile: "index.html",
  position: { x: 640, y: 320 },
  context: "Section: header",
  
  // Analyse source
  sourceAnalysis: {
    estimatedLine: "Ligne ~45 (Section header)",
    searchInstructions: [...],
    debugReport: {...}
  },
  
  // Fichiers à modifier
  filesToModify: [
    { path: "index.html", reason: "Structure HTML" },
    { path: "css/main.css", reason: "Styles personnalisés" }
  ],
  
  // Interactions détectées
  interactions: [
    { type: "Alpine.js", details: "x-data directive" },
    { type: "Bootstrap", details: "Classes CSS" }
  ],
  
  // Balises stratégiques
  strategicTags: {
    found: true,
    comment: "<!-- STRATEGIC-TAG:header|main-nav|25 -->",
    lineNumber: 25,
    sectionName: "header",
    elementId: "main-nav"
  }
}
```

### Export JSON

```javascript
{
  metadata: {
    timestamp: "2025-06-22T15:30:00Z",
    version: "2.1",
    inspector: "DOM Inspector Ultra"
  },
  element: {
    // Données de l'élément
  },
  analysis: {
    // Analyse complète
  },
  instructions: {
    searchPatterns: [...],
    modificationSteps: [...]
  }
}
```

## 🔧 Intégration Avancée

### Avec React

```jsx
import { useEffect } from 'react';

function MyComponent() {
  useEffect(() => {
    // Initialiser Alpine.js si nécessaire
    if (window.Alpine && !window.Alpine.data('domInspector')) {
      // Charger le DOM Inspector
    }
  }, []);
  
  return <div x-data="domInspector">...</div>;
}
```

### Avec Vue.js

```vue
<template>
  <div x-data="domInspector">
    <!-- Votre contenu -->
  </div>
</template>

<script>
export default {
  mounted() {
    // DOM Inspector disponible
  }
}
</script>
```

## 🛠️ Hooks et Extensions

### Hook d'Analyse Personnalisée

```javascript
// Étendre l'analyse
Alpine.data('customInspector', () => ({
  ...Alpine.data('domInspector')(),
  
  async analyzeElement(element) {
    const baseAnalysis = await this.constructor.prototype.analyzeElement.call(this, element);
    
    // Ajouter analyse personnalisée
    baseAnalysis.customData = {
      // Vos données personnalisées
    };
    
    return baseAnalysis;
  }
}));
```

## 📞 Support API

Pour toute question sur l'API :
1. Consultez les exemples dans `examples/`
2. Vérifiez la console pour les erreurs
3. Consultez `TROUBLESHOOTING.md`

---

**🚀 API DOM Inspector Ultra v2.1 - Puissance et Flexibilité Maximales !**
