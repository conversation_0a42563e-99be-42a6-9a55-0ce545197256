# 🎨 Guide de Personnalisation - DOM Inspector Ultra v2.1

**Personnalisez l'apparence et le comportement du DOM Inspector selon vos besoins**

## 🎯 Vue d'Ensemble

Le DOM Inspector Ultra est entièrement personnalisable via CSS, JavaScript et configuration Alpine.js.

## 🎨 Personnalisation Visuelle

### Variables CSS Principales

```css
:root {
  /* Couleurs du tooltip */
  --tooltip-bg: rgba(30, 41, 59, 0.95);          /* Fond du tooltip */
  --tooltip-border: #3b82f6;                      /* Bordure */
  --tooltip-text: #f8fafc;                        /* Texte principal */
  --tooltip-accent: #10b981;                      /* Couleur d'accent */
  --tooltip-warning: #f59e0b;                     /* Couleur d'avertissement */
  
  /* Effets visuels */
  --tooltip-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);  /* Ombre */
  --tooltip-radius: 8px;                              /* Bordures arrondies */
  --tooltip-z-index: 10000;                           /* Z-index */
  
  /* Dimensions */
  --tooltip-max-width: 400px;                     /* Largeur maximale */
  --tooltip-min-width: 250px;                     /* Largeur minimale */
  --tooltip-max-height: 80vh;                     /* Hauteur maximale */
}
```

### Thèmes Prédéfinis

#### Thème Sombre (par défaut)
```css
:root {
  --tooltip-bg: rgba(30, 41, 59, 0.95);
  --tooltip-border: #3b82f6;
  --tooltip-text: #f8fafc;
  --tooltip-accent: #10b981;
}
```

#### Thème Clair
```css
:root {
  --tooltip-bg: rgba(255, 255, 255, 0.95);
  --tooltip-border: #e5e7eb;
  --tooltip-text: #1f2937;
  --tooltip-accent: #3b82f6;
  --tooltip-warning: #f59e0b;
}
```

#### Thème Coloré
```css
:root {
  --tooltip-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --tooltip-border: #8b5cf6;
  --tooltip-text: #ffffff;
  --tooltip-accent: #fbbf24;
  --tooltip-warning: #f87171;
}
```

#### Thème Minimaliste
```css
:root {
  --tooltip-bg: rgba(0, 0, 0, 0.8);
  --tooltip-border: transparent;
  --tooltip-text: #ffffff;
  --tooltip-accent: #ffffff;
  --tooltip-warning: #fbbf24;
  --tooltip-radius: 4px;
  --tooltip-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### Personnalisation du Bouton d'Activation

#### Position du Bouton
```css
/* Bas droite (par défaut) */
.dom-inspector-toggle {
  bottom: 20px !important;
  right: 20px !important;
}

/* Bas gauche */
.dom-inspector-toggle {
  bottom: 20px !important;
  left: 20px !important;
  right: auto !important;
}

/* Haut droite */
.dom-inspector-toggle {
  top: 20px !important;
  right: 20px !important;
  bottom: auto !important;
}

/* Centré en bas */
.dom-inspector-toggle {
  bottom: 20px !important;
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
}
```

#### Style du Bouton
```css
/* Bouton moderne */
.dom-inspector-toggle {
  background: linear-gradient(45deg, #667eea, #764ba2) !important;
  border: none !important;
  border-radius: 50px !important;
  padding: 15px 25px !important;
  font-weight: 600 !important;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3) !important;
  transition: all 0.3s ease !important;
}

.dom-inspector-toggle:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4) !important;
}

/* Bouton minimaliste */
.dom-inspector-toggle {
  background: rgba(0, 0, 0, 0.7) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  padding: 10px 15px !important;
  font-size: 12px !important;
  backdrop-filter: blur(10px) !important;
}

/* Bouton circulaire */
.dom-inspector-toggle {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 24px !important;
}
```

### Personnalisation du Tooltip

#### Taille et Position
```css
/* Tooltip plus grand */
.dom-inspector-tooltip {
  max-width: 600px !important;
  min-width: 350px !important;
  max-height: 90vh !important;
}

/* Tooltip compact */
.dom-inspector-tooltip {
  max-width: 300px !important;
  min-width: 200px !important;
  font-size: 11px !important;
  padding: 8px 12px !important;
}

/* Position fixe */
.dom-inspector-tooltip {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  transform: none !important;
}
```

#### Animations Personnalisées
```css
/* Animation d'apparition personnalisée */
@keyframes customFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) rotate(-5deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

.dom-inspector-tooltip.visible {
  animation: customFadeIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Animation de pulsation */
@keyframes customPulse {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% { 
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

.dom-inspector-tooltip.locked {
  animation: customPulse 2s infinite;
}
```

## 🔧 Personnalisation Comportementale

### Configuration Alpine.js

#### Données Personnalisées
```html
<body x-data="{ 
  ...domInspector, 
  
  // Configuration personnalisée
  config: {
    autoHide: true,
    showLineNumbers: true,
    enableShortcuts: true,
    theme: 'dark'
  },
  
  // Méthodes personnalisées
  customAnalyze(element) {
    // Logique d'analyse personnalisée
  }
}">
```

#### Hooks Personnalisés
```javascript
// Étendre le comportement
document.addEventListener('alpine:init', () => {
  Alpine.data('customInspector', () => ({
    ...Alpine.data('domInspector')(),
    
    // Override de méthodes
    async analyzeElement(element) {
      const analysis = await this.constructor.prototype.analyzeElement.call(this, element);
      
      // Ajouter données personnalisées
      analysis.customMetrics = this.calculateCustomMetrics(element);
      
      return analysis;
    },
    
    calculateCustomMetrics(element) {
      return {
        complexity: this.getComplexityScore(element),
        performance: this.getPerformanceScore(element),
        accessibility: this.getA11yScore(element)
      };
    }
  }));
});
```

### Raccourcis Clavier Personnalisés

```javascript
// Ajouter des raccourcis personnalisés
document.addEventListener('keydown', (e) => {
  if (!domInspectorInstance?.isActive) return;
  
  // CTRL+ALT+D : Debug mode
  if (e.ctrlKey && e.altKey && e.key === 'd') {
    e.preventDefault();
    console.log('Debug mode activé');
    // Logique de debug
  }
  
  // CTRL+ALT+E : Export avancé
  if (e.ctrlKey && e.altKey && e.key === 'e') {
    e.preventDefault();
    // Export personnalisé
  }
});
```

## 🎯 Personnalisation Avancée

### Filtres d'Éléments

```javascript
// Filtrer les éléments inspectables
Alpine.data('selectiveInspector', () => ({
  ...Alpine.data('domInspector')(),
  
  handleMouseMove(e) {
    const element = e.target;
    
    // Ignorer certains éléments
    if (this.shouldIgnoreElement(element)) {
      this.hideTooltip();
      return;
    }
    
    // Appeler la méthode originale
    this.constructor.prototype.handleMouseMove.call(this, e);
  },
  
  shouldIgnoreElement(element) {
    // Ignorer les éléments avec certaines classes
    if (element.classList.contains('no-inspect')) return true;
    
    // Ignorer les éléments trop petits
    const rect = element.getBoundingClientRect();
    if (rect.width < 10 || rect.height < 10) return true;
    
    // Ignorer les éléments cachés
    if (getComputedStyle(element).display === 'none') return true;
    
    return false;
  }
}));
```

### Formatage Personnalisé

```javascript
// Personnaliser le format du rapport
Alpine.data('customReportInspector', () => ({
  ...Alpine.data('domInspector')(),
  
  formatElementReport(info) {
    return `
🎯 RAPPORT PERSONNALISÉ - ${new Date().toLocaleString()}
═══════════════════════════════════════════════════════

📍 ÉLÉMENT: <${info.tagName.toLowerCase()}${info.id ? ' id="' + info.id + '"' : ''}${info.classes ? ' class="' + info.classes + '"' : ''}>

🔍 ANALYSE PERSONNALISÉE:
• Complexité: ${this.calculateComplexity(info)}
• Performance: ${this.calculatePerformance(info)}
• Accessibilité: ${this.calculateAccessibility(info)}

📊 MÉTRIQUES:
• Enfants: ${info.childrenCount || 0}
• Profondeur: ${info.depth || 0}
• Taille: ${info.size || 'N/A'}

${this.constructor.prototype.formatElementReport.call(this, info)}
    `;
  }
}));
```

### Intégration avec Frameworks

#### Avec Bootstrap
```css
/* Adapter au thème Bootstrap */
:root {
  --tooltip-bg: var(--bs-dark);
  --tooltip-border: var(--bs-primary);
  --tooltip-text: var(--bs-light);
  --tooltip-accent: var(--bs-success);
  --tooltip-warning: var(--bs-warning);
}

.dom-inspector-toggle {
  background: var(--bs-primary) !important;
  border: 1px solid var(--bs-primary-border-subtle) !important;
}
```

#### Avec Tailwind CSS
```css
/* Utiliser les classes Tailwind */
.dom-inspector-tooltip {
  @apply bg-gray-900 border-blue-500 text-gray-100 rounded-lg shadow-2xl;
}

.dom-inspector-toggle {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-full shadow-lg transition-all duration-300;
}
```

## 📱 Responsive Design

### Adaptation Mobile
```css
/* Styles pour mobile */
@media (max-width: 768px) {
  .dom-inspector-tooltip {
    max-width: 90vw !important;
    min-width: 280px !important;
    font-size: 11px !important;
    padding: 10px !important;
  }
  
  .dom-inspector-toggle {
    bottom: 10px !important;
    right: 10px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
  }
  
  .tooltip-content-wrapper {
    max-height: 50vh !important;
  }
}

/* Styles pour tablette */
@media (min-width: 769px) and (max-width: 1024px) {
  .dom-inspector-tooltip {
    max-width: 350px !important;
  }
}
```

### Mode Sombre/Clair Automatique
```css
/* Adaptation au mode sombre du système */
@media (prefers-color-scheme: dark) {
  :root {
    --tooltip-bg: rgba(30, 41, 59, 0.95);
    --tooltip-text: #f8fafc;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --tooltip-bg: rgba(255, 255, 255, 0.95);
    --tooltip-text: #1f2937;
    --tooltip-border: #e5e7eb;
  }
}
```

## 🔧 Configuration Avancée

### Fichier de Configuration
```javascript
// config/dom-inspector-config.js
window.DOMInspectorConfig = {
  theme: 'dark', // 'dark', 'light', 'auto'
  position: 'bottom-right', // 'bottom-right', 'bottom-left', 'top-right', 'top-left'
  shortcuts: {
    toggle: 'ctrl+shift+i',
    copy: 'ctrl+c',
    lock: 'ctrl+q',
    export: 'ctrl+shift+c'
  },
  features: {
    strategicTags: true,
    lineNumbers: true,
    jsonExport: true,
    autoHide: false
  },
  ui: {
    showAnimations: true,
    compactMode: false,
    showHelp: true
  }
};
```

## 📞 Support Personnalisation

Pour des besoins de personnalisation avancés :
1. Consultez les exemples dans `examples/`
2. Vérifiez `API.md` pour les méthodes disponibles
3. Testez vos modifications avec `demo.html`

---

**🎨 DOM Inspector Ultra v2.1 - Personnalisable à l'Infini !**
