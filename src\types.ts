
import { Timestamp } from 'firebase/firestore';

export interface Plant {
  id: string;
  name: string;
  species?: string;
  description?: string;
  coverImageUrl?: string;
  createdAt: Timestamp;
}

export interface GeminiDiagnosis {
  isHealthy: boolean;
  disease: string;
  description: string;
  treatmentPlan: {
    steps: string[];
    treatmentFrequencyDays: number;
    recommendedProducts: {
      name: string;
      type: string;
      dosages: {
        pulverisateur_1L: string;
        pulverisateur_5L: string;
        arrosoir_11L: string;
        arrosoir_13L: string;
        pulverisateur_16L: string;
      };
      applicationMethod: string;
      precautions: string;
    }[];
  };
  careTips: string[];
}

export interface DiagnosticRecord {
  id: string;
  plantId: string;
  userId: string;
  timestamp: Timestamp;
  imageUrls: string[];
  diagnosis: GeminiDiagnosis;
  nextTreatmentDate?: Timestamp;
}

// Types pour l'archivage automatique
export interface ArchiveData {
  year: number;
  userId: string;
  plants: Plant[];
  diagnostics: DiagnosticRecord[];
  archivedAt: Timestamp;
  totalPlants: number;
  totalDiagnostics: number;
  geminiAccessible: boolean;
}

export interface ArchiveStats {
  totalArchives: number;
  oldestArchive: number;
  newestArchive: number;
  totalArchivedPlants: number;
  totalArchivedDiagnostics: number;
}

export interface GeminiLearningData {
  year: number;
  userId: string;
  summary: {
    totalPlants: number;
    totalDiagnostics: number;
    commonDiseases: { disease: string; count: number }[];
    plantTypes: { type: string; count: number }[];
    seasonalPatterns: { season: string; diseases: string[] }[];
  };
  accessibleForLearning: boolean;
  archivedAt: Timestamp;
}

// This is a workaround for process.env
// In a Vite project, you'd use import.meta.env
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      [key: string]: string | undefined;
    }
  }
}
