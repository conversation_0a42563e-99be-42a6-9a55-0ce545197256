# Problèmes de Couleurs Identifiés - FloraSynth

**Date :** 2025-07-25
**Problème :** Incohérences de couleurs dans les pages Notifications, Journal, IA Gemini

## Charte Graphique de Référence

### Couleurs Principales de l'Application
- **Arrière-plan principal :** `#100f1c` (violet très sombre)
- **Conteneurs/Cartes :** `#1c1a31` (violet légèrement plus clair)
- **Accent primaire :** `#d385f5` (gradient rose-lavande)
- **Texte principal :** `#FFFFFF` (blanc pur)
- **Texte secondaire :** `#E0E0E0` (gris très clair)

## Problèmes Identifiés

### 1. GeminiSettings.tsx - Fonctionnalités IA activées

**Lignes 222-242 :** Section "Fonctionnalités IA activées"

#### Problèmes :
```typescript
// ❌ PROBLÈME : Couleurs vertes au lieu des couleurs de l'app
<div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
  <span className="text-sm">Calculs d'échéances intelligents selon la saison</span>
</div>
```

#### Corrections nécessaires :
- `bg-green-50` → `bg-[#1c1a31]` (fond des cartes)
- `border-green-200` → `border-[#d385f5]` (bordure accent)
- `bg-green-500` → `bg-[#d385f5]` (indicateur)
- `text-sm` → `text-sm text-[#E0E0E0]` (texte secondaire)

### 2. GeminiSettings.tsx - Bouton de sauvegarde

**Lignes 248-271 :** Bouton "Sauvegarder"

#### Problèmes :
```typescript
// ❌ PROBLÈME : Couleurs vertes au lieu des couleurs de l'app
className={`flex items-center gap-2 ${
  isSaved ? 'bg-green-600 hover:bg-green-700' : ''
}`}
```

#### Corrections nécessaires :
- `bg-green-600` → `bg-[#d385f5]`
- `hover:bg-green-700` → `hover:bg-[#c070e0]`

### 3. NotificationCenter.tsx - Couleurs de priorité

**Lignes 264-272 :** Fonction `getPriorityColor`

#### Problèmes :
```typescript
// ❌ PROBLÈME : Couleurs claires sur fond sombre
case 'urgent': return 'border-l-red-500 bg-red-50';
case 'high': return 'border-l-orange-500 bg-orange-50';
case 'medium': return 'border-l-yellow-500 bg-yellow-50';
case 'low': return 'border-l-green-500 bg-green-50';
```

#### Corrections nécessaires :
- Remplacer les `bg-*-50` par des couleurs sombres compatibles
- Adapter les couleurs de bordure pour le thème sombre

### 4. GlobalJournal.tsx - Couleurs des actions

**Lignes 348-366 :** Composant `ActionHistoryItem`

#### Problèmes :
```typescript
// ❌ PROBLÈME : Texte sombre sur fond sombre
<span className="font-medium text-gray-900">{getActionLabel(action.actionType)}</span>
<p className="text-gray-700 text-sm mb-2">{action.description}</p>
```

#### Corrections nécessaires :
- `text-gray-900` → `text-white`
- `text-gray-700` → `text-[#E0E0E0]`
- `text-gray-500` → `text-gray-400`

### 5. GlobalJournal.tsx - Couleurs des événements

**Lignes 417-439 :** Composant `EventItem`

#### Problèmes similaires :
- Textes sombres sur fond sombre
- Couleurs non adaptées au thème

## Plan de Correction

### Étape 1 : Créer des constantes de couleurs
```typescript
// Créer un fichier src/styles/colors.ts
export const THEME_COLORS = {
  background: {
    primary: '#100f1c',
    secondary: '#1c1a31',
  },
  accent: {
    primary: '#d385f5',
    hover: '#c070e0',
  },
  text: {
    primary: '#FFFFFF',
    secondary: '#E0E0E0',
    muted: '#9CA3AF',
  },
  status: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  }
} as const;
```

### Étape 2 : Corriger GeminiSettings.tsx
- Remplacer toutes les couleurs vertes
- Utiliser les couleurs de la charte graphique

### Étape 3 : Corriger NotificationCenter.tsx
- Adapter les couleurs de priorité pour le thème sombre
- Utiliser des couleurs de fond sombres

### Étape 4 : Corriger GlobalJournal.tsx
- Remplacer les textes sombres par des textes clairs
- Adapter toutes les couleurs au thème sombre

### Étape 5 : Vérification globale
- Rechercher d'autres occurrences de couleurs inadaptées
- Tester l'affichage sur toutes les pages
- Valider la cohérence visuelle

## Priorité des Corrections

1. **Haute :** GeminiSettings.tsx (boutons verts très visibles)
2. **Haute :** GlobalJournal.tsx (texte illisible)
3. **Moyenne :** NotificationCenter.tsx (couleurs de priorité)
4. **Basse :** Autres incohérences mineures
