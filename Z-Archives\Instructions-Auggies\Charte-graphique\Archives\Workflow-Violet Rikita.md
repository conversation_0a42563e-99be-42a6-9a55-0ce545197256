## PROJET : "Violet Rikita" \-  Workflow de Développement Initial

**\*\*À l'attention d'Auggies, AUGMENT CODE\*\***

La mission est de construire le squelette complet d'une application web de soin pour les plantes, nommée "Violet Rikita". Cette application doit être interactive, visuellement attrayante et construite sur une architecture moderne et évolutive. Elle servira de fondation à un système hybride qui communiquera plus tard avec un Gem personnalisé.

**IMPORTANT :** **Le mode \[Developer Inspector Mode\] doit être activé pour ce projet.**

### **Section 1 : Environnement & Stack Technologique**

1. **Initialisation du Projet :**  
   * Crée un nouveau projet en utilisant Vite.js.  
     npm create vite@latest violet-rikita \-- \--template vanilla  
     cd violet-rikita  
     npm install

   * Installe les dépendances nécessaires :  
     npm install tailwindcss postcss autoprefixer alpinejs gsap firebase

2. **Configuration de la Stack :**  
   * **Structure :** HTML5  
   * **Style :** Tailwind CSS  
   * **État & Interactivité :** Alpine.js  
   * **Animations :** GSAP  
   * **Backend & Déploiement :** Firebase (Authentication, Firestore, Cloud Storage, Cloud Functions, Hosting)  
   * **Build Tool :** Vite.js  
3. **Mise en Place de Tailwind CSS :**  
   * Exécute npx tailwindcss init \-p pour générer les fichiers tailwind.config.js et postcss.config.js.  
   * Configure le tailwind.config.js pour scanner les fichiers HTML et JS.  
   * Crée et configure le fichier CSS principal (src/main.css) avec les directives @tailwind.

### **Section 2 : Architecture du Projet (Structure des Dossiers)**

Crée la structure de dossiers suivante, conçue pour être claire, compartimentée et évolutive.

/violet-rikita  
|-- /dist                   \# Dossier de build final  
|-- /src                    \# Code source de l'application  
|   |-- /assets  
|   |   |-- /images         \# Logos, fonds d'écran, etc.  
|   |   |-- /icons          \# Icônes SVG pour l'UI et le calculateur  
|   |-- /lib  
|   |   |-- firebase.js     \# Config et initialisation de Firebase  
|   |   |-- auth.js         \# Logique d'authentification (Google Sign-In)  
|   |   |-- database.js     \# Fonctions Firestore (CRUD pour plantes, diagnostics)  
|   |   |-- storage.js      \# Fonctions Cloud Storage (upload d'images)  
|   |-- /services  
|   |   |-- GeminiService.js \# Logique pour appeler la Cloud Function d'analyse  
|   |   |-- CalendarService.js\# Logique pour interagir avec Google Calendar API  
|   |-- /styles  
|   |   |-- main.css        \# Fichier CSS principal avec les directives Tailwind  
|   |-- main.js             \# Point d'entrée principal (init Alpine.js, etc.)  
|-- /functions              \# Dossier pour les Cloud Functions Firebase  
|   |-- /diagnosePlant  
|   |   |-- index.js        \# Le code de la fonction pour analyser les plantes  
|   |   |-- package.json  
|-- index.html              \# Page d'accueil / Point d'entrée  
|-- package.json  
|-- vite.config.js  
|-- tailwind.config.js  
|-- postcss.config.js  
|-- .gitignore  
|-- README.md

### **Section 3 : Configuration du Backend Firebase**

1. **Projet Firebase :** Crée un nouveau projet sur la console Firebase.  
2. **Services à Activer :**  
   * **Authentication :** Active la méthode de connexion "Google".  
   * **Firestore Database :** Crée une base de données en mode production.  
   * **Storage :** Active Cloud Storage.  
   * **Functions :** Initialise les Cloud Functions.  
3. **Fichier de Configuration :** Copie l'objet de configuration de ton projet Firebase et colle-le dans src/lib/firebase.js.  
4. **Règles de Sécurité (ultra important) :** Configure les règles suivantes dans ta console Firebase pour sécuriser l'accès aux données.  
   * **Pour Firestore (firestore.rules) :**  
     rules\_version \= '2';  
     service cloud.firestore {  
       match /databases/{database}/documents {  
         // Les utilisateurs peuvent uniquement lire/écrire leurs propres données  
         match /utilisateurs/{userId} {  
           allow read, write: if request.auth.uid \== userId;  
         }  
         // Les utilisateurs peuvent uniquement lire/écrire leurs propres plantes  
         match /plantes/{planteId} {  
           allow read, write: if request.auth.uid \== resource.data.proprietaireId;  
             // Les diagnostics suivent les permissions de la plante parente  
             match /diagnostics/{diagnosticId} {  
                 allow read, write: if request.auth.uid \== get(/databases/$(database)/documents/plantes/$(planteId)).data.proprietaireId;  
             }  
         }  
         // La base de données des engrais est en lecture seule pour les utilisateurs authentifiés  
         match /engrais/{engraisId} {  
             allow read: if request.auth \!= null;  
             allow write: if false; // Personne ne peut la modifier depuis le client  
         }  
       }  
     }

   * **Pour Cloud Storage (storage.rules) :**  
     rules\_version \= '2';  
     service firebase.storage {  
       match /b/{bucket}/o {  
         // Autoriser l'écriture uniquement pour les images dans le dossier d'un utilisateur, et si la taille est raisonnable  
         match /diagnostics/{userId}/{path=\*\*} {  
           allow read: if request.auth.uid \== userId;  
           allow write: if request.auth.uid \== userId && request.resource.size \< 5 \* 1024 \* 1024 // 5MB max  
                        && request.resource.contentType.matches('image/.\*');  
         }  
       }  
     }

### **Section 4 : Modèle de Données & Contenu Initial**

1. **Structure Firestore :** Implémente la logique dans src/lib/database.js pour interagir avec la structure suivante :  
   * utilisateurs/{userId}  
   * plantes/{planteId}  
     * Champs: nomPlante, espece, photoProfilURL, proprietaireId, dateAjout, etatGeneral  
     * Sous-collection : diagnostics/{diagnosticId}  
       * Champs : dateDiagnostic, photosSymptomes (array), resultatAnalyseGemini (map), traitementRecommande (map), dateProchainRappel (timestamp, **calculé automatiquement par la Cloud Function pour le suivi**)  
2. **Base de Données des Engrais :** Crée une collection engrais dans Firestore et remplis-la avec les documents suivants.  
   * *Document 1 :*  
     * **id:** sulfate-de-fer  
     * **nom:** "Sulfate de Fer (II)"  
     * **frequence:** 15  
     * **dosages:** \[{ "methode": "Arrosage au sol", "dose": "15-20 g/m²", "instructions": "Mélanger au sol lors du bêchage ou saupoudrer, puis arroser." }, { "methode": "Pulvérisation foliaire", "dose": "2-3 g / 1 L d'eau", "instructions": "Appliquer sur les feuilles jaunes." }\]  
   * *Document 2 :*  
     * **id:** sulfate-de-magnesium  
     * **nom:** "Sulfate de Magnésium (Sel d'Epsom)"  
     * **frequence:** 15  
     * **dosages:** \[{ "methode": "Arrosage au sol", "dose": "20 g / 10 L d'eau", "instructions": "Arroser autour des plantes." }, { "methode": "Pulvérisation foliaire", "dose": "1 c.à.c / 1 L d'eau", "instructions": "Vaporiser sur les feuilles 1 à 2 fois par mois." }\]  
   * *Document 3 :*  
     * **id:** sulfate-de-potassium  
     * **nom:** "Sulfate de Potassium"  
     * **frequence:** 21  
     * **dosages:** \[{ "methode": "Arrosage au sol", "dose": "20-40 g/m²", "instructions": "Répartir uniformément et incorporer si possible." }\]  
   * *Document 4 :*  
     * **id:** uree-azote  
     * **nom:** "Urée (Engrais Azoté)"  
     * **frequence:** 21  
     * **dosages:** \[{ "methode": "Arrosage au sol", "dose": "20-30 g/m²", "instructions": "Répartir sur sol humide et incorporer si possible." }\]  
   * *Document 5 :*  
     * **id:** phosphate-naturel  
     * **nom:** "Phosphate Naturel (Poudre d'os)"  
     * **frequence:** 180  
     * **dosages:** \[{ "methode": "Arrosage au sol", "dose": "50 g/m²", "instructions": "Incorporer à la terre lors de la plantation pour favoriser les racines." }\]  
   * *Document 6 :*  
     * **id:** purin-ortie  
     * **nom:** "Purin d'ortie"  
     * **frequence:** 15  
     * **dosages:** \[{ "methode": "Arrosage (fertilisant)", "dose": "Dilution 10% (1L pour 10L d'eau)", "instructions": "Arroser au pied des plantes tous les 15 jours." }, { "methode": "Pulvérisation (répulsif)", "dose": "Dilution 5% (0.5L pour 10L d'eau)", "instructions": "Pulvériser sur le feuillage contre pucerons et maladies." }\]  
   * *Document 7 :*  
     * **id:** amendement-calcaire  
     * **nom:** "Amendement Calcaire (Coquilles d'huîtres broyées)"  
     * **frequence:** 180  
     * **dosages:** \[{ "methode": "Amendement de fond", "dose": "100 g/m²", "instructions": "Mélanger à la terre à l'automne ou au printemps pour un apport de calcium à libération lente et corriger l'acidité." }\]  
   * *Document 8 :*  
     * **id:** soufre-elementaire  
     * **nom:** "Soufre Élémentaire (Fleur de soufre)"  
     * **frequence:** 90  
     * **dosages:** \[{ "methode": "Acidification du sol", "dose": "30-50 g/m²", "instructions": "Pour les plantes acidophiles (hortensias, rhododendrons). Incorporer légèrement au sol. Ne pas utiliser comme engrais direct." }, { "methode": "Action fongicide", "dose": "5g / 1 L d'eau", "instructions": "Pulvériser en prévention de l'oïdium (maladie du blanc)." }\]  
   * *Document 9 :*  
     * **id:** oligo-elements-mix  
     * **nom:** "Mélange d'oligo-éléments"  
     * **frequence:** 180  
     * **dosages:** \[{ "methode": "Prévention des carences", "dose": "5-10 g / 10 L d'eau", "instructions": "Arroser au pied des plantes 1 à 2 fois par an pour assurer un apport complet en micronutriments (Bore, Cuivre, Manganèse, Zinc...)." }\]  
3. Option \- Rendre la base de données extensible (Phase 2\) :  
   Pour permettre l'ajout de nouveaux engrais à l'avenir sans modifier le code, une interface d'administration simple pourra être créée. Celle-ci serait une page protégée dans l'application, accessible uniquement par vous, permettant de remplir un formulaire pour ajouter, modifier ou supprimer un document dans la collection engrais de Firestore.

### **Section 5 : Logique Applicative & Interface**

1. **Authentification :** Dans index.html et auth.js, crée une interface qui affiche un bouton "Se connecter avec Google" si l'utilisateur n'est pas connecté, et le contenu de l'application (la liste des plantes) s'il l'est.  
2. **Affichage des Plantes :** Crée un composant de carte (inspiré des captures d'écran) avec Tailwind CSS. Utilise Alpine.js pour boucler sur les données des plantes récupérées de Firestore et afficher une carte pour chaque plante. Anime l'apparition des cartes avec GSAP.  
3. **Processus de Diagnostic (Cloud Function)** \- Le Cerveau Botaniste **:**  
   * Crée la Cloud Function diagnosePlant (en Node.js).  
   * **Logique de la fonction :**  
     1. Récupérer les URLs des 3 images depuis le document déclencheur.  
     2. Récupérer la liste des engrais disponibles depuis la collection Firestore engrais.  
     3. Appeler l'API Gemini avec un prompt multimodal détaillé :  
        "Tu es 'Violette', une IA botaniste, amicale et pédagogue. Ton but est d'aider un jardinier à soigner sa plante sans stress. Analyse ces 3 photos (vue d'ensemble, rapprochée, macro).  
        1. Identifie la maladie ou la carence la plus probable.  
        2. Explique en termes simples et encourageants ce qui se passe pour la plante.  
        3. Propose un traitement en choisissant l'engrais le plus adapté dans cette liste : \[Insérer ici la liste des noms et IDs d'engrais\].  
        4. Donne un avertissement clair sur les risques (ex: surdosage, mauvaise période d'application) pour rassurer et guider l'utilisateur.

     
           Formate ta réponse finale uniquement en JSON valide avec les clés suivantes : {\\"maladie\_identifiee\\": \\"Nom de la maladie\\", \\"explication\_simple\\": \\"Voici ce qui arrive à ta plante...\\", \\"avertissement\\": \\"Attention à ne pas...\\", \\"traitement\_recommande\\": {\\"produit\_id\\": \\"id-du-produit-firestore\\", \\"instructions\_specifiques\\": \\"Voici exactement comment appliquer le traitement...\\"}}"

     4. À partir de la réponse JSON de Gemini, lire le produit\_id recommandé.  
     5. Interroger la collection engrais pour trouver le document correspondant à ce produit\_id et récupérer sa frequence (en jours).  
     6. Calculer la date du prochain rappel : dateProchainRappel \= dateDuJour \+ frequence (en timestamp).  
     7. Mettre à jour le document Firestore du diagnostic avec le JSON de Gemini ET le dateProchainRappel calculé.  
4. Parcours Utilisateur Post-Diagnostic \- L'Accompagnement Interactif :  
   * Une fois le diagnostic terminé (via l'écouteur onSnapshot), l'interface ne doit pas tout afficher d'un coup. Crée une séquence interactive animée avec GSAP/Alpine.js :  
     1. Étape 1 : Le Verdict. Une carte apparaît avec le nom de la maladie et l'explication simple (maladie\_identifiee, explication\_simple). Un bouton "Compris, quelle est la solution ?" apparaît.  
     2. Étape 2 : Le Plan d'Action. Au clic, la carte s'agrandit ou une nouvelle apparaît, montrant le traitement recommandé (traitement\_recommande). La visualisation du dosage (cf. point 5\) s'anime ici. Un message d'avertissement (avertissement) est mis en évidence. Un bouton "J'ai bien noté, et ensuite ?" apparaît.  
     3. Étape 3 : Le Suivi. Au clic, le système de rappel (cf. point 6\) est présenté à l'utilisateur comme l'étape finale logique pour ne rien oublier.  
5. **Visualisation du Dosage :**  
   * Crée des icônes SVG animées pour un arrosoir et un sac d'engrais.  
   * Quand le plan d'action s'affiche, utilise les données pour animer une visualisation claire du mélange, rendant le dosage ludique et facile à comprendre.  
6. **Système de Rappels (Google Calendar) \- Fonctionnalité Clé :**  
   * **Interface :** À l'étape 3 du parcours post-diagnostic, affiche un message clair : "Pour ne pas oublier, je peux ajouter un rappel dans votre calendrier pour le prochain traitement le \[Date\]." avec un bouton "Oui, créer le rappel".  
   * **Logique (CalendarService.js) :** Gère l'authentification OAuth2 et la création de l'événement dans le calendrier de l'utilisateur avec toutes les informations pertinentes.

### **Section 6 : Déploiement & Gestion de Version**

1. **GitHub :**  
   * Initialise un dépôt Git local : git init.  
   * Crée un nouveau dépôt sur GitHub.  
   * Effectue un premier commit : git add . puis git commit \-m "Initial commit: Project architecture and Firebase setup".  
   * Pousse le code sur GitHub.  
2. **Déploiement Firebase :**  
   * Connecte-toi à Firebase : firebase login.  
   * Initialise le projet : firebase init (en sélectionnant Hosting, Functions, Firestore, Storage).  
   * Déploie l'application et les fonctions : firebase deploy.

Ce workflow constitue la feuille de route complète pour la création de la V1 de "Violet Rikita".