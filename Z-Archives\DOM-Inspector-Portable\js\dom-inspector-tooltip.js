/**
 * 🔍 DOM Inspector Tooltip - FlexoDiv Site
 * Système d'inspection visuelle des éléments DOM avec tooltip informatif
 * Compatible avec Alpine.js et la stack technique autorisée
 * 
 * @version 1.0.0
 * <AUTHOR>
 * @date 2025-06-22
 */

document.addEventListener('alpine:init', () => {
    Alpine.data('domInspector', () => ({
        // État de l'inspecteur
        isActive: false,
        tooltip: null,
        currentElement: null,
        isTooltipLocked: false, // Nouveau : état de verrouillage du tooltip
        tooltipScrollPosition: 0, // Nouveau : position de scroll du tooltip
        
        // Initialisation
        init() {
            this.createTooltip();
            this.createToggleButton();
            console.log('🔍 DOM Inspector initialisé - FlexoDiv Site');
        },
        
        // Créer le tooltip
        createTooltip() {
            this.tooltip = document.createElement('div');
            this.tooltip.className = 'dom-inspector-tooltip';
            this.tooltip.innerHTML = `
                <div class="tooltip-header">
                    <span class="tooltip-title">Élément DOM</span>
                    <span class="tooltip-tag">FlexoDiv</span>
                    <span class="tooltip-lock-indicator" style="display: none;">🔒</span>
                </div>
                <div class="tooltip-content-wrapper">
                    <div class="tooltip-content"></div>
                </div>
                <div class="tooltip-actions">
                    <button class="tooltip-btn copy-btn" onclick="domInspectorInstance.copyElementInfo()">
                        📋 Copier Toutes les Infos
                    </button>
                </div>
                <div class="tooltip-help" style="display: none;">
                    <small>🔒 Ctrl+Q: Verrouiller/Déverrouiller | ↑↓: Défiler</small>
                </div>
            `;
            document.body.appendChild(this.tooltip);
        },
        
        // Créer le bouton toggle
        createToggleButton() {
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'dom-inspector-toggle';
            toggleBtn.innerHTML = '🔍 Inspecteur DOM';
            toggleBtn.onclick = () => this.toggle();
            document.body.appendChild(toggleBtn);
        },
        
        // Activer/désactiver l'inspecteur
        toggle() {
            this.isActive = !this.isActive;
            const toggleBtn = document.querySelector('.dom-inspector-toggle');
            
            if (this.isActive) {
                document.body.classList.add('dom-inspector-active');
                toggleBtn.classList.add('active');
                toggleBtn.innerHTML = '❌ Désactiver';
                this.attachEvents();
            } else {
                document.body.classList.remove('dom-inspector-active');
                toggleBtn.classList.remove('active');
                toggleBtn.innerHTML = '🔍 Inspecteur DOM';
                this.detachEvents();
                this.hideTooltip();
            }
        },
        
        // Attacher les événements
        attachEvents() {
            document.addEventListener('mouseover', this.handleMouseOver.bind(this));
            document.addEventListener('mouseout', this.handleMouseOut.bind(this));
            document.addEventListener('mousemove', this.handleMouseMove.bind(this));
            document.addEventListener('click', this.handleClick.bind(this));
            document.addEventListener('keydown', this.handleKeyDown.bind(this));
        },

        // Détacher les événements
        detachEvents() {
            document.removeEventListener('mouseover', this.handleMouseOver.bind(this));
            document.removeEventListener('mouseout', this.handleMouseOut.bind(this));
            document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
            document.removeEventListener('click', this.handleClick.bind(this));
            document.removeEventListener('keydown', this.handleKeyDown.bind(this));
        },
        
        // Gérer le survol (VERSION ASYNCHRONE)
        async handleMouseOver(e) {
            if (!this.isActive || this.isTooltipElement(e.target)) return;

            this.currentElement = e.target;
            e.target.classList.add('dom-inspector-highlight');
            await this.updateTooltipContent(e.target);
            this.showTooltip();
        },
        
        // Gérer la sortie du survol
        handleMouseOut(e) {
            if (!this.isActive || this.isTooltipElement(e.target)) return;
            
            e.target.classList.remove('dom-inspector-highlight');
        },
        
        // Gérer le mouvement de la souris (VERSION AMÉLIORÉE AVEC DÉTECTION AUTOMATIQUE)
        handleMouseMove(e) {
            if (!this.isActive || !this.tooltip || this.isTooltipLocked) return;

            // Calculer la position optimale du tooltip avec détection automatique
            const optimalPosition = this.calculateOptimalTooltipPosition(e);

            this.tooltip.style.left = optimalPosition.x + 'px';
            this.tooltip.style.top = optimalPosition.y + 'px';

            // Ajouter une classe CSS pour indiquer la position choisie (pour le styling)
            this.tooltip.className = this.tooltip.className.replace(/position-\w+/g, '');
            this.tooltip.classList.add(`position-${optimalPosition.placement}`);
        },

        // NOUVELLE FONCTION : Calculer la position optimale du tooltip avec détection automatique
        calculateOptimalTooltipPosition(e) {
            const mouseX = e.clientX;
            const mouseY = e.clientY;
            const rect = this.tooltip.getBoundingClientRect();
            const tooltipWidth = rect.width;
            const tooltipHeight = rect.height;

            // Dimensions de la fenêtre
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;

            // Marges de sécurité
            const margin = 20;
            const offset = 15; // Distance entre la souris et le tooltip

            // Positions possibles avec leurs priorités
            const positions = [
                {
                    name: 'bottom-right',
                    x: mouseX + offset,
                    y: mouseY + offset,
                    priority: 1 // Position par défaut préférée
                },
                {
                    name: 'top-right',
                    x: mouseX + offset,
                    y: mouseY - tooltipHeight - offset,
                    priority: 2
                },
                {
                    name: 'bottom-left',
                    x: mouseX - tooltipWidth - offset,
                    y: mouseY + offset,
                    priority: 3
                },
                {
                    name: 'top-left',
                    x: mouseX - tooltipWidth - offset,
                    y: mouseY - tooltipHeight - offset,
                    priority: 4
                },
                {
                    name: 'right-center',
                    x: mouseX + offset,
                    y: mouseY - (tooltipHeight / 2),
                    priority: 5
                },
                {
                    name: 'left-center',
                    x: mouseX - tooltipWidth - offset,
                    y: mouseY - (tooltipHeight / 2),
                    priority: 6
                }
            ];

            // Évaluer chaque position et choisir la meilleure
            let bestPosition = null;
            let bestScore = -1;

            for (const position of positions) {
                const score = this.evaluateTooltipPosition(position, windowWidth, windowHeight, margin);

                if (score > bestScore) {
                    bestScore = score;
                    bestPosition = position;
                }
            }

            // Si aucune position n'est parfaite, ajuster la meilleure position trouvée
            if (bestScore < 100) {
                bestPosition = this.adjustTooltipPosition(bestPosition, windowWidth, windowHeight, margin);
            }

            return {
                x: Math.max(margin, Math.min(bestPosition.x, windowWidth - tooltipWidth - margin)),
                y: Math.max(margin, Math.min(bestPosition.y, windowHeight - tooltipHeight - margin)),
                placement: bestPosition.name,
                score: bestScore
            };
        },

        // NOUVELLE FONCTION : Évaluer la qualité d'une position de tooltip
        evaluateTooltipPosition(position, windowWidth, windowHeight, margin) {
            const tooltipRect = this.tooltip.getBoundingClientRect();
            const tooltipWidth = tooltipRect.width;
            const tooltipHeight = tooltipRect.height;

            let score = 100; // Score parfait de base

            // Pénalités pour sortir de l'écran
            if (position.x < margin) {
                score -= Math.abs(position.x - margin) * 2;
            }
            if (position.x + tooltipWidth > windowWidth - margin) {
                score -= Math.abs((position.x + tooltipWidth) - (windowWidth - margin)) * 2;
            }
            if (position.y < margin) {
                score -= Math.abs(position.y - margin) * 2;
            }
            if (position.y + tooltipHeight > windowHeight - margin) {
                score -= Math.abs((position.y + tooltipHeight) - (windowHeight - margin)) * 2;
            }

            // Bonus pour les positions préférées
            score += (10 - position.priority) * 5;

            // Pénalité si le tooltip couvre la souris (gênant pour l'utilisateur)
            const mouseX = position.x - 15; // Approximation de la position de la souris
            const mouseY = position.y - 15;

            if (mouseX >= position.x && mouseX <= position.x + tooltipWidth &&
                mouseY >= position.y && mouseY <= position.y + tooltipHeight) {
                score -= 50; // Forte pénalité
            }

            return Math.max(0, score);
        },

        // NOUVELLE FONCTION : Ajuster une position de tooltip pour qu'elle reste dans l'écran
        adjustTooltipPosition(position, windowWidth, windowHeight, margin) {
            const tooltipRect = this.tooltip.getBoundingClientRect();
            const tooltipWidth = tooltipRect.width;
            const tooltipHeight = tooltipRect.height;

            const adjustedPosition = { ...position };

            // Ajustement horizontal
            if (adjustedPosition.x < margin) {
                adjustedPosition.x = margin;
                adjustedPosition.name = adjustedPosition.name.replace('left', 'right');
            } else if (adjustedPosition.x + tooltipWidth > windowWidth - margin) {
                adjustedPosition.x = windowWidth - tooltipWidth - margin;
                adjustedPosition.name = adjustedPosition.name.replace('right', 'left');
            }

            // Ajustement vertical
            if (adjustedPosition.y < margin) {
                adjustedPosition.y = margin;
                adjustedPosition.name = adjustedPosition.name.replace('top', 'bottom');
            } else if (adjustedPosition.y + tooltipHeight > windowHeight - margin) {
                adjustedPosition.y = windowHeight - tooltipHeight - margin;
                adjustedPosition.name = adjustedPosition.name.replace('bottom', 'top');
            }

            return adjustedPosition;
        },
        
        // Gérer le clic (empêcher la navigation)
        handleClick(e) {
            if (!this.isActive || this.isTooltipElement(e.target)) return;
            e.preventDefault();
            e.stopPropagation();
        },

        // Gérer les raccourcis clavier (VERSION ULTRA-AMÉLIORÉE)
        handleKeyDown(e) {
            if (!this.isActive) return;

            // CTRL+Q pour verrouiller/déverrouiller le tooltip
            if (e.ctrlKey && e.key === 'q') {
                e.preventDefault();
                e.stopPropagation();
                this.toggleTooltipLock();
                return;
            }

            // Gestion du scroll dans le tooltip verrouillé
            if (this.isTooltipLocked && this.tooltip && this.tooltip.classList.contains('visible')) {
                if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    this.scrollTooltip(-20);
                    return;
                }
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    this.scrollTooltip(20);
                    return;
                }
            }

            if (!this.currentElement) return;

            // CTRL+C pour copier les informations de l'élément
            if (e.ctrlKey && e.key === 'c' && !e.shiftKey) {
                e.preventDefault();
                e.stopPropagation();
                this.copyElementInfo();
            }

            // CTRL+SHIFT+C pour export JSON complet
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                e.stopPropagation();
                this.exportElementJSON();
            }

            // CTRL+ALT+S pour générer une balise stratégique
            if (e.ctrlKey && e.altKey && e.key === 's') {
                e.preventDefault();
                e.stopPropagation();
                this.generateAndCopyStrategicTag();
            }

            // Échap pour désactiver l'inspecteur ou déverrouiller le tooltip
            if (e.key === 'Escape') {
                if (this.isTooltipLocked) {
                    this.toggleTooltipLock();
                } else {
                    this.toggle();
                }
            }
        },
        
        // Vérifier si l'élément fait partie du tooltip
        isTooltipElement(element) {
            return element.closest('.dom-inspector-tooltip') ||
                   element.closest('.dom-inspector-toggle') ||
                   element.closest('.copy-success');
        },

        // NOUVELLE FONCTION : Verrouiller/Déverrouiller le tooltip
        toggleTooltipLock() {
            this.isTooltipLocked = !this.isTooltipLocked;
            const lockIndicator = this.tooltip.querySelector('.tooltip-lock-indicator');
            const helpText = this.tooltip.querySelector('.tooltip-help');

            if (this.isTooltipLocked) {
                this.tooltip.classList.add('locked');
                this.tooltip.style.pointerEvents = 'auto';
                lockIndicator.style.display = 'inline';
                helpText.style.display = 'block';
                console.log('🔒 Tooltip verrouillé - Utilisez les flèches ↑↓ pour défiler');
            } else {
                this.tooltip.classList.remove('locked');
                this.tooltip.style.pointerEvents = 'none';
                lockIndicator.style.display = 'none';
                helpText.style.display = 'none';
                this.tooltipScrollPosition = 0;
                const content = this.tooltip.querySelector('.tooltip-content');
                if (content) content.scrollTop = 0;
                console.log('🔓 Tooltip déverrouillé');
            }
        },

        // NOUVELLE FONCTION : Faire défiler le contenu du tooltip
        scrollTooltip(delta) {
            const content = this.tooltip.querySelector('.tooltip-content');
            if (!content) return;

            this.tooltipScrollPosition += delta;
            this.tooltipScrollPosition = Math.max(0, Math.min(
                this.tooltipScrollPosition,
                content.scrollHeight - content.clientHeight
            ));

            content.scrollTop = this.tooltipScrollPosition;
        },
        
        // Mettre à jour le contenu du tooltip (VERSION ULTRA-AMÉLIORÉE)
        async updateTooltipContent(element) {
            const info = await this.analyzeElement(element);
            const content = this.tooltip.querySelector('.tooltip-content');

            content.innerHTML = `
                <div class="tooltip-section">
                    <div class="tooltip-label">Élément</div>
                    <div class="tooltip-value tooltip-element-info">
                        &lt;${info.tagName.toLowerCase()}${info.id ? ' id="' + info.id + '"' : ''}${info.classes ? ' class="' + info.classes + '"' : ''}&gt;
                    </div>
                </div>

                <div class="tooltip-section">
                    <div class="tooltip-label">📍 Localisation Précise</div>
                    <ul class="tooltip-list">
                        <li><span class="tooltip-file-path">Fichier HTML: ${info.htmlFile}</span></li>
                        <li><span class="tooltip-dependency">Position écran: ${info.position}</span></li>
                        <li><span class="tooltip-dependency">Contexte: ${info.context}</span></li>
                        <li><span class="tooltip-line-number">📍 Position code: ${info.sourceAnalysis.estimatedLine}</span></li>
                        ${info.strategicTags.found ?
                            `<li><span class="tooltip-strategic-tag">🎯 Balise stratégique: Ligne ${info.strategicTags.lineNumber}</span></li>` :
                            info.strategicTags.shouldAdd ?
                                `<li><span class="tooltip-strategic-suggestion priority-${info.strategicTags.priority}">💡 Balise recommandée (${info.strategicTags.priority}): ${info.strategicTags.reasons?.join(', ')}</span></li>` :
                                `<li><span class="tooltip-no-tag">⚠️ Aucune balise stratégique détectée</span></li>`
                        }
                    </ul>
                </div>

                <div class="tooltip-section">
                    <div class="tooltip-label">🔍 Instructions de Recherche</div>
                    <ul class="tooltip-list">
                        ${info.sourceAnalysis.searchInstructions.map(instruction =>
                            `<li><span class="tooltip-search-type">${instruction.type}:</span> <span class="tooltip-file-path">${instruction.pattern}</span></li>`
                        ).join('')}
                    </ul>
                </div>

                <div class="tooltip-section">
                    <div class="tooltip-label">📁 Fichiers à Modifier</div>
                    <ul class="tooltip-list">
                        ${info.filesToModify.map(file => `<li><span class="tooltip-file-path">${file.path}</span> <span class="tooltip-dependency">(${file.reason})</span></li>`).join('')}
                    </ul>
                </div>

                <div class="tooltip-section">
                    <div class="tooltip-label">🔗 Interactions Détectées</div>
                    <ul class="tooltip-list">
                        ${info.interactions.map(interaction => `<li><span class="tooltip-dependency">${interaction}</span></li>`).join('')}
                    </ul>
                </div>

                <div class="tooltip-section">
                    <div class="tooltip-label">🎯 Sélecteur CSS</div>
                    <div class="tooltip-value tooltip-file-path">${info.selector}</div>
                </div>

                ${!info.strategicTags.found && info.strategicTags.shouldAdd ? `
                <div class="tooltip-section tooltip-strategic-recommendation priority-${info.strategicTags.priority}">
                    <div class="tooltip-label">🏷️ Balise Stratégique Recommandée (Priorité: ${info.strategicTags.priority.toUpperCase()})</div>
                    <div class="tooltip-value tooltip-strategic-suggestion">
                        ${info.strategicTags.suggestion || this.generateStrategicTag(element, info.sourceAnalysis.estimatedLine.match(/\d+/)?.[0] || '100').recommended}
                    </div>
                    <div class="tooltip-value" style="font-size: 9px; color: #f59e0b;">
                        💡 Raisons: ${info.strategicTags.reasons?.join(', ') || 'Élément important détecté'}
                    </div>
                    <div class="tooltip-value" style="font-size: 8px; color: #10b981;">
                        ⌨️ Utilisez CTRL+ALT+S pour générer et copier automatiquement
                    </div>
                </div>
                ` : !info.strategicTags.found ? `
                <div class="tooltip-section">
                    <div class="tooltip-label">🏷️ Balise Stratégique (Optionnelle)</div>
                    <div class="tooltip-value" style="font-size: 9px; color: #64748b;">
                        Cet élément ne nécessite pas de balise stratégique prioritaire
                    </div>
                </div>
                ` : ''}

                <div class="tooltip-section">
                    <div class="tooltip-label">⌨️ Raccourcis</div>
                    <div class="tooltip-value" style="font-size: 10px; color: #10b981;">
                        CTRL+C → Copier rapport complet<br>
                        CTRL+SHIFT+C → Export JSON avancé<br>
                        CTRL+ALT+S → Générer balise stratégique<br>
                        ÉCHAP → Désactiver inspecteur
                    </div>
                </div>
            `;
        },
        
        // Analyser l'élément pour extraire les informations (VERSION AMÉLIORÉE)
        async analyzeElement(element) {
            const tagName = element.tagName;
            const id = element.id;
            const classes = Array.from(element.classList).join(' ');

            // Générer un sélecteur CSS unique
            let selector = tagName.toLowerCase();
            if (id) selector += `#${id}`;
            if (classes) selector += `.${classes.split(' ').join('.')}`;

            // Déterminer le fichier HTML source
            const htmlFile = this.determineHtmlFile(element);

            // Déterminer la position dans la page
            const position = this.getElementPosition(element);

            // Déterminer le contexte de l'élément
            const context = this.getElementContext(element);

            // Déterminer les fichiers à modifier
            const filesToModify = this.getFilesToModify(element);

            // Détecter les interactions
            const interactions = this.detectInteractions(element);

            // NOUVELLE ANALYSE : Position exacte dans le code source
            const sourceAnalysis = await this.analyzeElementSourcePosition(element);

            // NOUVELLE ANALYSE : Détection des balises stratégiques
            const strategicTags = this.detectStrategicTags(element);

            return {
                tagName,
                id,
                classes,
                selector,
                htmlFile,
                position,
                context,
                filesToModify,
                interactions,
                sourceAnalysis, // Nouvelles données de localisation précise
                strategicTags // Détection des balises stratégiques
            };
        },
        
        // Déterminer le fichier HTML source avec analyse avancée
        determineHtmlFile(element) {
            const url = window.location.pathname;
            let htmlFile = 'index.html'; // Par défaut

            if (url === '/' || url.endsWith('index.html')) {
                htmlFile = 'index.html';
            } else if (url.includes('portfolio')) {
                htmlFile = 'pages/portfolio/portfolio-4-columns.html';
            } else if (url.includes('blog')) {
                htmlFile = 'pages/blog/blog.html';
            } else if (url.includes('contact')) {
                htmlFile = 'pages/contact_form/contact.html';
            }

            return htmlFile;
        },

        // NOUVELLE FONCTION : Analyser la position exacte dans le code source
        async analyzeElementSourcePosition(element) {
            try {
                // Créer un identifiant unique pour l'élément
                const elementSignature = this.createElementSignature(element);

                // Simuler l'analyse du code source (en production, ceci ferait un appel à un service)
                const sourceAnalysis = this.simulateSourceCodeAnalysis(element, elementSignature);

                return sourceAnalysis;
            } catch (error) {
                console.warn('Erreur lors de l\'analyse de la position source:', error);
                return {
                    estimatedLine: 'Non déterminé',
                    codeContext: 'Analyse impossible',
                    searchPattern: this.createElementSignature(element)
                };
            }
        },

        // NOUVELLE FONCTION : Créer une signature unique pour l'élément
        createElementSignature(element) {
            const tagName = element.tagName.toLowerCase();
            const id = element.id;
            const classes = Array.from(element.classList);
            const textContent = element.textContent ? element.textContent.trim().substring(0, 50) : '';

            // Créer un pattern de recherche unique
            let signature = `<${tagName}`;
            if (id) signature += ` id="${id}"`;
            if (classes.length > 0) signature += ` class="${classes.join(' ')}"`;
            signature += `>`;

            return {
                htmlPattern: signature,
                textContent: textContent,
                tagName: tagName,
                id: id,
                classes: classes
            };
        },

        // NOUVELLE FONCTION : Simuler l'analyse du code source (VERSION ULTRA-AMÉLIORÉE)
        simulateSourceCodeAnalysis(element, signature) {
            const htmlFile = this.determineHtmlFile(element);

            // Analyse DOM avancée
            const advancedAnalysis = this.performAdvancedDOMAnalysis(signature);

            // Analyse basée sur la position dans le DOM
            const domPosition = this.calculateDOMPosition(element);

            // Instructions de recherche combinées
            const basicInstructions = this.generateSearchInstructions(element, signature);
            const advancedInstructions = advancedAnalysis.searchPatterns || [];
            const searchInstructions = [...basicInstructions, ...advancedInstructions];

            // Estimation de ligne améliorée
            let estimatedLine = 'Non déterminé';
            if (htmlFile === 'index.html') {
                const basicEstimate = this.estimateLineInIndexHtml(element, domPosition);
                const advancedEstimate = advancedAnalysis.estimatedLine;
                estimatedLine = `${basicEstimate} | ${advancedEstimate}`;
            }

            // Contexte enrichi
            const codeContext = `${advancedAnalysis.codeContext} | DOM Position: ${domPosition}`;

            // Rapport de débogage
            const debugReport = this.generateDebugReport(element);

            return {
                estimatedLine,
                codeContext,
                searchInstructions,
                domPosition,
                signature,
                advancedAnalysis,
                debugReport
            };
        },

        // NOUVELLE FONCTION : Calculer la position dans le DOM (VERSION CORRIGÉE)
        calculateDOMPosition(element) {
            let position = 0;
            let current = element;

            // Compter les éléments précédents
            while (current.previousElementSibling) {
                current = current.previousElementSibling;
                position++;
            }

            // Ajouter la position du parent avec pondération réduite
            const parent = element.parentElement;
            if (parent && parent !== document.body && parent !== document.documentElement) {
                position += this.calculateDOMPosition(parent) * 5; // Pondération réduite de 100 à 5
            }

            return Math.min(position, 1000); // Limiter à 1000 pour éviter les valeurs aberrantes
        },

        // NOUVELLE FONCTION : Estimer la ligne dans index.html
        estimateLineInIndexHtml(element, domPosition) {
            // Mapping approximatif des sections principales d'index.html
            const sectionMapping = {
                'header': '20-50',
                'home-page': '50-150',
                'services': '150-250',
                'portfolio': '250-350',
                'testimonials': '350-400',
                'clients': '400-450',
                'fun-facts': '450-500',
                'pricing': '500-600',
                'contact': '600-700',
                'footer': '700-800'
            };

            // Trouver la section parent
            const section = element.closest('section, header, footer, nav');
            if (section) {
                const sectionId = section.id || section.className.split(' ')[0];
                for (const [key, range] of Object.entries(sectionMapping)) {
                    if (sectionId.toLowerCase().includes(key)) {
                        return `Lignes ${range} (Section ${key})`;
                    }
                }
            }

            // Estimation basée sur la position DOM (VERSION CORRIGÉE)
            const estimatedLine = Math.floor(50 + Math.min(domPosition * 0.5, 400)); // Facteur réduit et plafonné
            return `Ligne ~${estimatedLine} (Estimation DOM)`;
        },

        // NOUVELLE FONCTION : Générer des instructions de recherche précises
        generateSearchInstructions(element, signature) {
            const instructions = [];

            // Instruction basée sur l'ID
            if (signature.id) {
                instructions.push({
                    type: 'ID',
                    pattern: `id="${signature.id}"`,
                    description: `Rechercher l'attribut id="${signature.id}" dans le fichier`
                });
            }

            // Instruction basée sur les classes uniques
            if (signature.classes.length > 0) {
                const uniqueClasses = signature.classes.filter(cls =>
                    !['col-', 'row', 'container', 'btn'].some(common => cls.includes(common))
                );
                if (uniqueClasses.length > 0) {
                    instructions.push({
                        type: 'CLASS',
                        pattern: `class="${uniqueClasses.join(' ')}"`,
                        description: `Rechercher les classes spécifiques: ${uniqueClasses.join(', ')}`
                    });
                }
            }

            // Instruction basée sur le contenu textuel
            if (signature.textContent && signature.textContent.length > 10) {
                instructions.push({
                    type: 'TEXT',
                    pattern: signature.textContent,
                    description: `Rechercher le texte: "${signature.textContent}"`
                });
            }

            // Instruction basée sur la structure
            instructions.push({
                type: 'STRUCTURE',
                pattern: signature.htmlPattern,
                description: `Rechercher la balise: ${signature.htmlPattern}`
            });

            return instructions;
        },

        // NOUVELLE FONCTION : Analyser le code HTML en temps réel (EXPÉRIMENTAL)
        async analyzeHtmlSourceCode(htmlFile, elementSignature) {
            try {
                // Cette fonction tente de récupérer le code source HTML
                // En production, ceci nécessiterait un service backend ou une API

                // Pour l'instant, nous simulons avec une analyse DOM avancée
                const domAnalysis = this.performAdvancedDOMAnalysis(elementSignature);

                return {
                    success: true,
                    lineNumber: domAnalysis.estimatedLine,
                    codeSnippet: domAnalysis.codeContext,
                    searchPatterns: domAnalysis.searchPatterns
                };
            } catch (error) {
                console.warn('Analyse du code source impossible:', error);
                return {
                    success: false,
                    error: error.message,
                    fallback: 'Utiliser les patterns de recherche fournis'
                };
            }
        },

        // NOUVELLE FONCTION : Analyse DOM avancée
        performAdvancedDOMAnalysis(elementSignature) {
            // Analyse plus poussée du DOM pour estimer la position
            const element = this.currentElement;
            if (!element) return { estimatedLine: 'Non disponible', codeContext: '', searchPatterns: [] };

            // Calculer la profondeur dans le DOM
            let depth = 0;
            let current = element;
            while (current.parentElement) {
                depth++;
                current = current.parentElement;
            }

            // Analyser les éléments frères pour estimer la position
            const siblings = Array.from(element.parentElement?.children || []);
            const siblingIndex = siblings.indexOf(element);

            // Créer des patterns de recherche plus précis
            const searchPatterns = [];

            // Pattern basé sur la structure parent-enfant
            if (element.parentElement) {
                const parentTag = element.parentElement.tagName.toLowerCase();
                const parentId = element.parentElement.id;
                const parentClass = element.parentElement.className;

                if (parentId) {
                    searchPatterns.push({
                        type: 'PARENT_ID',
                        pattern: `<${parentTag} id="${parentId}">`,
                        description: `Chercher dans le parent avec ID: ${parentId}`
                    });
                }

                if (parentClass) {
                    const mainClass = parentClass.split(' ')[0];
                    searchPatterns.push({
                        type: 'PARENT_CLASS',
                        pattern: `<${parentTag} class="${mainClass}`,
                        description: `Chercher dans le parent avec classe: ${mainClass}`
                    });
                }
            }

            // Pattern basé sur les attributs uniques
            if (element.id) {
                searchPatterns.push({
                    type: 'UNIQUE_ID',
                    pattern: `id="${element.id}"`,
                    description: `Recherche directe par ID unique`
                });
            }

            // Pattern basé sur le contenu textuel unique
            const textContent = element.textContent?.trim();
            if (textContent && textContent.length > 15 && textContent.length < 100) {
                searchPatterns.push({
                    type: 'UNIQUE_TEXT',
                    pattern: textContent.substring(0, 50),
                    description: `Recherche par contenu textuel unique`
                });
            }

            // Estimation de ligne basée sur la structure
            let estimatedLine = 50; // Base
            estimatedLine += depth * 10; // Profondeur
            estimatedLine += siblingIndex * 2; // Position parmi les frères

            // Ajustement selon le type d'élément
            const tagName = element.tagName.toLowerCase();
            if (['header', 'nav'].includes(tagName)) estimatedLine = 20 + siblingIndex * 5;
            if (['section'].includes(tagName)) estimatedLine = 100 + siblingIndex * 50;
            if (['footer'].includes(tagName)) estimatedLine = 700 + siblingIndex * 10;

            return {
                estimatedLine: `Ligne ~${estimatedLine} (Analyse DOM avancée)`,
                codeContext: `Profondeur: ${depth}, Position: ${siblingIndex}/${siblings.length}`,
                searchPatterns: searchPatterns
            };
        },

        // NOUVELLE FONCTION : Générer un rapport de débogage complet
        generateDebugReport(element) {
            const report = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                element: {
                    tagName: element.tagName,
                    id: element.id,
                    className: element.className,
                    textContent: element.textContent?.substring(0, 100),
                    attributes: Array.from(element.attributes).map(attr => ({
                        name: attr.name,
                        value: attr.value
                    }))
                },
                dom: {
                    depth: this.calculateElementDepth(element),
                    siblings: element.parentElement?.children.length || 0,
                    position: Array.from(element.parentElement?.children || []).indexOf(element)
                },
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    scrollX: window.scrollX,
                    scrollY: window.scrollY
                }
            };

            return report;
        },

        // NOUVELLE FONCTION : Calculer la profondeur d'un élément
        calculateElementDepth(element) {
            let depth = 0;
            let current = element;
            while (current.parentElement) {
                depth++;
                current = current.parentElement;
            }
            return depth;
        },

        // NOUVELLE FONCTION : Détecter les balises stratégiques
        detectStrategicTags(element) {
            const strategicInfo = {
                found: false,
                lineNumber: null,
                sectionName: null,
                elementId: null,
                comment: null
            };

            // Chercher un commentaire stratégique avant l'élément
            let current = element;
            let attempts = 0;

            while (current && attempts < 10) { // Limiter la recherche
                const previousNode = current.previousSibling;

                if (previousNode && previousNode.nodeType === Node.COMMENT_NODE) {
                    const commentText = previousNode.textContent.trim();

                    // Détecter les patterns de balises stratégiques
                    const strategicPatterns = [
                        /DOM-INSPECTOR-LINE:(\d+)/i,
                        /FLEXODIV-SECTION:([^|]+)\|LINE:(\d+)/i,
                        /STRATEGIC-TAG:([^|]+)\|([^|]+)\|(\d+)/i
                    ];

                    for (const pattern of strategicPatterns) {
                        const match = commentText.match(pattern);
                        if (match) {
                            strategicInfo.found = true;
                            strategicInfo.comment = commentText;

                            if (pattern.source.includes('DOM-INSPECTOR-LINE')) {
                                strategicInfo.lineNumber = parseInt(match[1]);
                            } else if (pattern.source.includes('FLEXODIV-SECTION')) {
                                strategicInfo.sectionName = match[1];
                                strategicInfo.lineNumber = parseInt(match[2]);
                            } else if (pattern.source.includes('STRATEGIC-TAG')) {
                                strategicInfo.sectionName = match[1];
                                strategicInfo.elementId = match[2];
                                strategicInfo.lineNumber = parseInt(match[3]);
                            }

                            return strategicInfo;
                        }
                    }
                }

                current = current.parentElement;
                attempts++;
            }

            // Si aucune balise trouvée, analyser l'importance de l'élément
            if (!strategicInfo.found) {
                const importance = this.analyzeElementImportance(element);
                strategicInfo.suggestion = this.generateStrategicTagSuggestion(element);
                strategicInfo.shouldAdd = importance.shouldHaveTag;
                strategicInfo.priority = importance.priority;
                strategicInfo.reasons = importance.reasons;
            }

            return strategicInfo;
        },

        // NOUVELLE FONCTION : Analyser l'importance d'un élément
        analyzeElementImportance(element) {
            const importance = {
                shouldHaveTag: false,
                priority: 'low',
                reasons: []
            };

            // Critères de haute priorité
            if (element.tagName === 'SECTION' || element.tagName === 'MAIN' || element.tagName === 'HEADER' || element.tagName === 'FOOTER') {
                importance.shouldHaveTag = true;
                importance.priority = 'high';
                importance.reasons.push('Élément structurel principal');
            }

            // Navigation
            if (element.tagName === 'NAV' || element.classList.contains('nav') || element.classList.contains('menu')) {
                importance.shouldHaveTag = true;
                importance.priority = 'high';
                importance.reasons.push('Élément de navigation');
            }

            // Formulaires importants
            if (element.tagName === 'FORM' || element.classList.contains('contact') || element.classList.contains('form')) {
                importance.shouldHaveTag = true;
                importance.priority = 'medium';
                importance.reasons.push('Formulaire interactif');
            }

            // Conteneurs avec beaucoup de contenu
            if (element.children.length > 5) {
                importance.shouldHaveTag = true;
                importance.priority = 'medium';
                importance.reasons.push('Conteneur complexe');
            }

            // Éléments avec IDs significatifs
            if (element.id && (element.id.includes('section') || element.id.includes('container') || element.id.includes('wrapper'))) {
                importance.shouldHaveTag = true;
                importance.priority = 'medium';
                importance.reasons.push('ID significatif');
            }

            // Classes importantes
            const importantClasses = ['hero', 'about', 'services', 'portfolio', 'contact', 'testimonials', 'pricing'];
            const hasImportantClass = importantClasses.some(cls =>
                element.classList.contains(cls) ||
                Array.from(element.classList).some(c => c.includes(cls))
            );

            if (hasImportantClass) {
                importance.shouldHaveTag = true;
                importance.priority = 'high';
                importance.reasons.push('Classe de section importante');
            }

            return importance;
        },

        // NOUVELLE FONCTION : Générer une suggestion de balise stratégique
        generateStrategicTagSuggestion(element) {
            const tagName = element.tagName.toLowerCase();
            const id = element.id || '';
            const classes = Array.from(element.classList).join(' ');

            // Déterminer le type de section
            let sectionType = 'COMPONENT';
            if (element.tagName === 'SECTION') sectionType = 'SECTION';
            else if (element.tagName === 'HEADER') sectionType = 'HEADER';
            else if (element.tagName === 'FOOTER') sectionType = 'FOOTER';
            else if (element.tagName === 'NAV') sectionType = 'NAVIGATION';
            else if (element.tagName === 'FORM') sectionType = 'FORM';

            // Déterminer le nom basé sur l'ID ou les classes
            let sectionName = '';
            if (id) {
                sectionName = id.toUpperCase().replace(/[-_]/g, '-');
            } else if (classes) {
                const firstClass = classes.split(' ')[0];
                sectionName = firstClass.toUpperCase().replace(/[-_]/g, '-');
            } else {
                sectionName = tagName.toUpperCase();
            }

            // Générer une description
            let description = `${tagName} element`;
            if (id) description += ` with ID "${id}"`;
            if (classes) description += ` with classes "${classes}"`;

            return `<!-- 🎯 BALISE-STRATEGIQUE: ${sectionType}-${sectionName} - ${description} -->`;
        },

        // NOUVELLE FONCTION : Générer des balises stratégiques pour un élément
        generateStrategicTag(element, estimatedLine) {
            const tagName = element.tagName.toLowerCase();
            const id = element.id || 'no-id';
            const classes = Array.from(element.classList).slice(0, 2).join('-') || 'no-class';
            const section = this.getElementContext(element).replace('Section: ', '');

            // Générer différents formats de balises stratégiques
            const strategicTags = [
                `<!-- DOM-INSPECTOR-LINE:${estimatedLine} -->`,
                `<!-- FLEXODIV-SECTION:${section}|LINE:${estimatedLine} -->`,
                `<!-- STRATEGIC-TAG:${section}|${tagName}-${id}|${estimatedLine} -->`
            ];

            return {
                recommended: strategicTags[2], // Le plus complet
                alternatives: strategicTags,
                placement: 'before', // Placer avant l'élément
                description: `Balise stratégique pour ${tagName}#${id} dans ${section}`
            };
        },

        // Obtenir la position de l'élément dans la page
        getElementPosition(element) {
            const rect = element.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            return `X: ${Math.round(rect.left + scrollLeft)}px, Y: ${Math.round(rect.top + scrollTop)}px`;
        },

        // Obtenir le contexte de l'élément (section parent)
        getElementContext(element) {
            const parent = element.closest('section, header, footer, nav, main, aside');
            if (parent) {
                const parentId = parent.id || parent.className.split(' ')[0] || parent.tagName.toLowerCase();
                return `Section: ${parentId}`;
            }
            return 'Document racine';
        },

        // Déterminer les fichiers à modifier
        getFilesToModify(element) {
            const files = [];

            // Fichier HTML principal
            files.push({
                path: this.determineHtmlFile(element),
                reason: 'Structure HTML'
            });

            // Fichiers CSS basés sur les classes
            const classes = Array.from(element.classList);
            if (classes.length > 0) {
                files.push({
                    path: 'css/main.css',
                    reason: 'Styles personnalisés'
                });
            }

            // Fichiers spécifiques selon les classes
            classes.forEach(className => {
                if (className.includes('btn') || className.includes('col-')) {
                    files.push({
                        path: 'css/bootstrap.min.css',
                        reason: 'Framework Bootstrap'
                    });
                }
                if (className.includes('owl')) {
                    files.push({
                        path: 'css/owl.carousel.css',
                        reason: 'Carrousel'
                    });
                }
            });

            // Fichiers JavaScript si interactions détectées
            if (element.onclick || element.hasAttribute('data-toggle') || element.hasAttribute('x-data')) {
                files.push({
                    path: 'js/main.js',
                    reason: 'Interactions JavaScript'
                });
            }

            return files;
        },

        // Détecter les interactions
        detectInteractions(element) {
            const interactions = [];

            // Événements JavaScript
            if (element.onclick) interactions.push('Événement onclick');
            if (element.onmouseover) interactions.push('Événement onmouseover');

            // Attributs Bootstrap
            if (element.hasAttribute('data-toggle')) {
                interactions.push(`Bootstrap: ${element.getAttribute('data-toggle')}`);
            }
            if (element.hasAttribute('data-target')) {
                interactions.push(`Cible: ${element.getAttribute('data-target')}`);
            }

            // Attributs Alpine.js
            if (element.hasAttribute('x-data')) {
                interactions.push(`Alpine.js: ${element.getAttribute('x-data')}`);
            }
            if (element.hasAttribute('x-show')) interactions.push('Alpine.js: Affichage conditionnel');
            if (element.hasAttribute('x-click')) interactions.push('Alpine.js: Événement click');

            // Liens et navigation
            if (element.tagName === 'A' && element.href) {
                interactions.push(`Navigation vers: ${element.href}`);
            }

            // Formulaires
            if (element.tagName === 'FORM') {
                interactions.push(`Formulaire: ${element.method || 'GET'} vers ${element.action || 'même page'}`);
            }
            if (element.type === 'submit') interactions.push('Bouton de soumission');

            return interactions.length > 0 ? interactions : ['Aucune interaction détectée'];
        },
        

        
        // Afficher le tooltip
        showTooltip() {
            this.tooltip.classList.add('visible');
        },
        
        // Masquer le tooltip
        hideTooltip() {
            this.tooltip.classList.remove('visible');
        },
        
        // Copier les informations de l'élément (VERSION ULTRA-PRÉCISE)
        async copyElementInfo() {
            if (!this.currentElement) return;

            const info = await this.analyzeElement(this.currentElement);
            const text = `
🔍 ÉLÉMENT DOM - FlexoDiv Site (RAPPORT ULTRA-PRÉCIS POUR CISCO)
═══════════════════════════════════════════════════════════════════

📍 ÉLÉMENT CIBLÉ:
<${info.tagName.toLowerCase()}${info.id ? ' id="' + info.id + '"' : ''}${info.classes ? ' class="' + info.classes + '"' : ''}>

📍 LOCALISATION ULTRA-PRÉCISE:
• Fichier HTML: ${info.htmlFile}
• Position dans le code: ${info.sourceAnalysis.estimatedLine}
• Position écran: ${info.position}
• Contexte DOM: ${info.context}
${info.strategicTags.found ?
    `• 🎯 BALISE STRATÉGIQUE DÉTECTÉE: Ligne ${info.strategicTags.lineNumber} (${info.strategicTags.comment})` :
    `• ⚠️ AUCUNE BALISE STRATÉGIQUE - Recommandation: ${this.generateStrategicTag(this.currentElement, info.sourceAnalysis.estimatedLine.match(/\d+/)?.[0] || '100').recommended}`
}

🔍 INSTRUCTIONS DE RECHERCHE EXACTES POUR AUGGIES:
${info.sourceAnalysis.searchInstructions.map(instruction =>
    `   ${instruction.type}: ${instruction.pattern}\n   → ${instruction.description}`
).join('\n\n')}

📁 FICHIERS À MODIFIER (ORDRE DE PRIORITÉ):
${info.filesToModify.map((file, index) => `   ${index + 1}. ${file.path} (${file.reason})`).join('\n')}

🔗 INTERACTIONS DÉTECTÉES:
${info.interactions.map(interaction => `   • ${interaction}`).join('\n')}

🎯 SÉLECTEUR CSS POUR CIBLAGE PRÉCIS:
${info.selector}

⚡ INSTRUCTIONS ULTRA-DÉTAILLÉES POUR AUGGIES:

ÉTAPE 1 - LOCALISATION:
• Ouvre le fichier: ${info.htmlFile}
• Va à la position: ${info.sourceAnalysis.estimatedLine}
• Utilise la recherche (Ctrl+F) avec ces patterns:
${info.sourceAnalysis.searchInstructions.map(instruction => `  - ${instruction.pattern}`).join('\n')}

ÉTAPE 2 - MODIFICATION:
• Structure HTML: Modifier dans ${info.htmlFile}
• Styles CSS: Modifier dans css/main.css
• Interactions JS: Vérifier js/main.js si nécessaire

ÉTAPE 3 - VALIDATION:
• Tester l'affichage visuel
• Vérifier les interactions: ${info.interactions.length > 1 ? 'OUI' : 'NON'}
• Valider la responsivité mobile

🎯 SIGNATURE UNIQUE DE L'ÉLÉMENT:
${JSON.stringify(info.sourceAnalysis.signature, null, 2)}

📊 RAPPORT DE DÉBOGAGE COMPLET:
${JSON.stringify(info.sourceAnalysis.debugReport, null, 2)}

═══════════════════════════════════════════════════════════════════
Généré par DOM Inspector FlexoDiv Ultra v2.0 - ${new Date().toLocaleString('fr-FR')}
COMMUNICATION CISCO ↔ AUGGIES À 100% OPTIMISÉE ✅

🚀 BONUS - EXPORT JSON POUR ANALYSE AVANCÉE:
Pour obtenir un export JSON complet, appuyez sur CTRL+SHIFT+C
            `.trim();

            // Stocker le texte pour la modal de fallback
            this.lastGeneratedText = text;
            this.copyToClipboard(text);
        },

        // NOUVELLE FONCTION : Export JSON complet pour analyse avancée
        async exportElementJSON() {
            if (!this.currentElement) return;

            const info = await this.analyzeElement(this.currentElement);

            const jsonExport = {
                meta: {
                    version: "FlexoDiv DOM Inspector Ultra v2.0",
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    userAgent: navigator.userAgent
                },
                element: {
                    tagName: info.tagName,
                    id: info.id,
                    classes: info.classes,
                    selector: info.selector,
                    textContent: this.currentElement.textContent?.substring(0, 200),
                    innerHTML: this.currentElement.innerHTML?.substring(0, 500),
                    attributes: Array.from(this.currentElement.attributes).map(attr => ({
                        name: attr.name,
                        value: attr.value
                    }))
                },
                location: {
                    htmlFile: info.htmlFile,
                    screenPosition: info.position,
                    context: info.context,
                    estimatedCodePosition: info.sourceAnalysis.estimatedLine
                },
                analysis: {
                    sourceAnalysis: info.sourceAnalysis,
                    filesToModify: info.filesToModify,
                    interactions: info.interactions,
                    debugReport: info.sourceAnalysis.debugReport
                },
                instructions: {
                    searchPatterns: info.sourceAnalysis.searchInstructions,
                    modificationSteps: [
                        `1. Ouvrir ${info.htmlFile}`,
                        `2. Rechercher avec: ${info.sourceAnalysis.searchInstructions[0]?.pattern || info.selector}`,
                        `3. Position estimée: ${info.sourceAnalysis.estimatedLine}`,
                        `4. Modifier les styles dans css/main.css`,
                        `5. Tester les interactions si présentes`
                    ]
                }
            };

            const jsonString = JSON.stringify(jsonExport, null, 2);

            // Copier le JSON dans le presse-papiers
            await this.copyToClipboard(jsonString);

            // Afficher un message spécifique pour l'export JSON
            this.showJSONExportSuccess();
        },

        // NOUVELLE FONCTION : Générer et copier une balise stratégique
        async generateAndCopyStrategicTag() {
            if (!this.currentElement) return;

            const info = await this.analyzeElement(this.currentElement);
            const estimatedLineNumber = info.sourceAnalysis.estimatedLine.match(/\d+/)?.[0] || '100';
            const strategicTag = this.generateStrategicTag(this.currentElement, estimatedLineNumber);

            const tagText = `
🏷️ BALISE STRATÉGIQUE GÉNÉRÉE - FlexoDiv DOM Inspector Ultra v2.1
═══════════════════════════════════════════════════════════════════

📍 ÉLÉMENT CIBLÉ:
<${info.tagName.toLowerCase()}${info.id ? ' id="' + info.id + '"' : ''}${info.classes ? ' class="' + info.classes + '"' : ''}>

🏷️ BALISE STRATÉGIQUE RECOMMANDÉE:
${strategicTag.recommended}

📋 INSTRUCTIONS D'IMPLÉMENTATION:
1. Copier la balise ci-dessus
2. Ouvrir le fichier: ${info.htmlFile}
3. Aller à la ligne: ${estimatedLineNumber}
4. Coller la balise AVANT l'élément ciblé
5. Sauvegarder le fichier

💡 ALTERNATIVES DISPONIBLES:
${strategicTag.alternatives.map((alt, index) => `   ${index + 1}. ${alt}`).join('\n')}

🎯 DESCRIPTION:
${strategicTag.description}

⚡ AVANTAGES APRÈS IMPLÉMENTATION:
• Localisation instantanée de l'élément
• Précision absolue (ligne exacte)
• Communication Cisco ↔ Auggies optimisée
• Plus de recherche manuelle nécessaire

═══════════════════════════════════════════════════════════════════
Généré par DOM Inspector FlexoDiv Ultra v2.1 - ${new Date().toLocaleString('fr-FR')}
SYSTÈME DE BALISES STRATÉGIQUES RÉVOLUTIONNAIRE ✅
            `.trim();

            // Copier dans le presse-papiers
            await this.copyToClipboard(tagText);

            // Afficher un message spécifique
            this.showStrategicTagSuccess();
        },

        // NOUVELLE FONCTION : Message de succès pour la balise stratégique
        showStrategicTagSuccess() {
            const successMsg = document.createElement('div');
            successMsg.className = 'copy-success strategic-tag';
            successMsg.innerHTML = '🏷️ Balise stratégique copiée !<br><small>Prête à être implémentée dans le code</small>';
            document.body.appendChild(successMsg);

            setTimeout(() => successMsg.classList.add('show'), 100);
            setTimeout(() => {
                successMsg.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(successMsg)) {
                        document.body.removeChild(successMsg);
                    }
                }, 300);
            }, 4000);
        },

        // NOUVELLE FONCTION : Message de succès pour l'export JSON
        showJSONExportSuccess() {
            const successMsg = document.createElement('div');
            successMsg.className = 'copy-success json-export';
            successMsg.innerHTML = '🚀 Export JSON copié !<br><small>Données complètes pour analyse avancée</small>';
            document.body.appendChild(successMsg);

            setTimeout(() => successMsg.classList.add('show'), 100);
            setTimeout(() => {
                successMsg.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(successMsg)) {
                        document.body.removeChild(successMsg);
                    }
                }, 300);
            }, 3000);
        },
        
        // Copier le sélecteur CSS
        copySelector() {
            if (!this.currentElement) return;
            
            const info = this.analyzeElement(this.currentElement);
            this.copyToClipboard(info.selector);
        },
        
        // Copier dans le presse-papiers
        async copyToClipboard(text) {
            // Vérifier si l'API Clipboard est disponible et si on est en HTTPS
            if (navigator.clipboard && window.isSecureContext) {
                try {
                    await navigator.clipboard.writeText(text);
                    this.showCopySuccess();
                    return;
                } catch (err) {
                    console.warn('Clipboard API failed, using fallback:', err);
                }
            }

            // Utiliser le fallback dans tous les autres cas
            this.fallbackCopyToClipboard(text);
        },
        
        // Fallback pour la copie
        fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            textArea.style.opacity = '0';
            textArea.setAttribute('readonly', '');
            textArea.setAttribute('aria-hidden', 'true');

            document.body.appendChild(textArea);

            // Sélectionner le texte
            textArea.focus();
            textArea.select();
            textArea.setSelectionRange(0, text.length);

            let success = false;
            try {
                success = document.execCommand('copy');
                if (success) {
                    this.showCopySuccess();
                } else {
                    this.showCopyError();
                }
            } catch (err) {
                console.error('Fallback copy failed:', err);
                this.showCopyError();
            }

            document.body.removeChild(textArea);
        },
        
        // Afficher le message de copie réussie
        showCopySuccess() {
            const successMsg = document.createElement('div');
            successMsg.className = 'copy-success';
            successMsg.textContent = '✅ Copié dans le presse-papiers !';
            document.body.appendChild(successMsg);

            setTimeout(() => successMsg.classList.add('show'), 100);
            setTimeout(() => {
                successMsg.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(successMsg)) {
                        document.body.removeChild(successMsg);
                    }
                }, 300);
            }, 2000);
        },

        // Afficher le message d'erreur de copie
        showCopyError() {
            // Afficher d'abord le message d'erreur
            const errorMsg = document.createElement('div');
            errorMsg.className = 'copy-error';
            errorMsg.innerHTML = '❌ Erreur de copie<br><small>Cliquez ici pour voir le texte</small>';
            errorMsg.style.cursor = 'pointer';
            document.body.appendChild(errorMsg);

            // Ajouter un événement de clic pour afficher la modal
            errorMsg.addEventListener('click', () => {
                this.showTextModal();
                document.body.removeChild(errorMsg);
            });

            setTimeout(() => errorMsg.classList.add('show'), 100);
            setTimeout(() => {
                errorMsg.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(errorMsg)) {
                        document.body.removeChild(errorMsg);
                    }
                }, 300);
            }, 5000);
        },

        // Afficher une modal avec le texte sélectionnable
        showTextModal() {
            if (!this.lastGeneratedText) return;

            const modal = document.createElement('div');
            modal.className = 'text-modal-overlay';
            modal.innerHTML = `
                <div class="text-modal">
                    <div class="text-modal-header">
                        <h3>📋 Informations de l'élément DOM</h3>
                        <button class="text-modal-close">✕</button>
                    </div>
                    <div class="text-modal-content">
                        <textarea readonly class="text-modal-textarea">${this.lastGeneratedText}</textarea>
                    </div>
                    <div class="text-modal-footer">
                        <small>Sélectionnez tout le texte (Ctrl+A) puis copiez (Ctrl+C)</small>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Fermer la modal
            const closeBtn = modal.querySelector('.text-modal-close');
            const overlay = modal;

            const closeModal = () => {
                modal.classList.add('closing');
                setTimeout(() => {
                    if (document.body.contains(modal)) {
                        document.body.removeChild(modal);
                    }
                }, 300);
            };

            closeBtn.addEventListener('click', closeModal);
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) closeModal();
            });

            // Sélectionner automatiquement le texte
            const textarea = modal.querySelector('.text-modal-textarea');
            setTimeout(() => {
                textarea.focus();
                textarea.select();
            }, 100);
        }
    }));
});

// Instance globale pour les boutons du tooltip
let domInspectorInstance = null;

// Initialiser quand Alpine est prêt
document.addEventListener('alpine:initialized', () => {
    // Créer une instance globale
    const inspectorElement = document.querySelector('[x-data="domInspector"]');
    if (inspectorElement) {
        domInspectorInstance = Alpine.$data(inspectorElement);
    } else {
        domInspectorInstance = {
            copyElementInfo: () => console.log('Inspector not initialized'),
            copySelector: () => console.log('Inspector not initialized')
        };
    }

    console.log('🎯 DOM Inspector FlexoDiv prêt ! Cliquez sur le bouton en haut à gauche pour commencer.');
});
