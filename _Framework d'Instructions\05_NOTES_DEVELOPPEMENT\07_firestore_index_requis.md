# 🔍 Index Firestore Requis - Violet Rikita

## 📊 Problème Identifié

**PROBLÈME CRITIQUE :** Les index actuels dans `firestore.indexes.json` sont configurés pour des **collection groups** mais le code utilise des **sous-collections**.

### **Erreurs Observées :**
```
FirebaseError: [code=failed-precondition]: The query requires an index. That index is currently building and cannot be used yet.
```

## 🎯 Index Composites Requis (SOUS-COLLECTIONS)

### **1. Sous-collection `users/{userId}/diagnostic_events`**

**Index requis pour les requêtes avec filtres :**

#### **A. Requête : completed + nextActionDate + orderBy**
```typescript
// notificationService.ts - getDiagnosticEvents avec pendingOnly
const q = query(
  collection(db, 'users', userId, 'diagnostic_events'),
  where('completed', '==', false),
  orderBy('nextActionDate', 'asc')
);
```

#### **B. Requête : plantId + nextActionDate + orderBy**
```typescript
// notificationService.ts - getDiagnosticEvents avec plantId
const q = query(
  collection(db, 'users', userId, 'diagnostic_events'),
  where('plantId', '==', filters.plantId),
  orderBy('nextActionDate', 'asc')
);
```

#### **C. Requête : status + nextActionDate + orderBy**
```typescript
// notificationService.ts - getDiagnosticEvents avec status
const q = query(
  collection(db, 'users', userId, 'diagnostic_events'),
  where('status', '==', filters.status),
  orderBy('nextActionDate', 'asc')
);
```

### **2. Sous-collection `users/{userId}/notifications`**

#### **A. Requête : read + createdAt + orderBy**
```typescript
// notificationService.ts - getUserNotifications avec unreadOnly
const q = query(
  collection(db, 'users', userId, 'notifications'),
  where('read', '==', false),
  orderBy('createdAt', 'desc')
);
```

### **3. Sous-collection `users/{userId}/action_history`**

#### **A. Requête : plantId + actionDate + orderBy**
```typescript
// notificationService.ts - getActionHistory avec plantId
const q = query(
  collection(db, 'users', userId, 'action_history'),
  where('plantId', '==', plantId),
  orderBy('actionDate', 'desc')
);
```

### **4. Sous-collection `users/{userId}/plants/{plantId}/diagnostics`**

#### **A. Requête : timestamp range + orderBy**
```typescript
// archiveService.ts - createArchive
const diagnosticsQuery = query(
  collection(db, 'users', userId, 'plants', plant.id, 'diagnostics'),
  where('timestamp', '>=', startDate),
  where('timestamp', '<', endDate),
  orderBy('timestamp', 'desc')
);
```

### **5. Collection `archives`**

#### **A. Requête : userId + year + orderBy**
```typescript
// archiveService.ts - getUserArchives
const archivesQuery = query(
  collection(db, 'archives'),
  where('userId', '==', userId),
  orderBy('year', 'desc')
);
```

## � SOLUTION : Corriger firestore.indexes.json

### **PROBLÈME IDENTIFIÉ :**
Le fichier `firestore.indexes.json` actuel utilise `"queryScope": "COLLECTION"` avec des `collectionGroup`, mais nos requêtes utilisent des **sous-collections spécifiques**.

### **CORRECTION REQUISE :**
Remplacer les index existants par des index pour sous-collections avec `"queryScope": "COLLECTION"`.

## ⚡ Actions à Effectuer

### **Étape 1 : Corriger firestore.indexes.json**
1. Remplacer le contenu actuel par les index corrects pour sous-collections
2. Déployer les nouveaux index avec Firebase CLI

### **Étape 2 : Déploiement Firebase**
```bash
firebase deploy --only firestore:indexes
```

### **Étape 3 : Vérification**
- Attendre la création des index (5-10 minutes)
- Vérifier dans Firebase Console > Firestore > Indexes
- Tester l'application

### **Étape 4 : Test Complet**
- Recharger l'application
- Tester les notifications
- Tester l'archivage
- Vérifier les logs d'erreur

## 🛡️ Sécurité

**Règles Firestore :**
- ✅ Sous-collections sécurisées par userId
- ✅ Collection `archives` avec validation `userId_year`
- ✅ Permissions restrictives par défaut

## 📝 Notes Techniques

### **Différence Collection vs Sous-collection :**
- **Collection Group :** `collectionGroup: "diagnostic_events"` (toutes les collections nommées ainsi)
- **Sous-collection :** `users/{userId}/diagnostic_events` (collection spécifique sous un document)

### **Impact Performance :**
- ⚡ Requêtes optimisées pour sous-collections
- 📉 Réduction drastique des erreurs d'index
- 🔄 Amélioration de la réactivité

## ✅ Checklist de Validation

- [ ] Fichier `firestore.indexes.json` corrigé
- [ ] Index déployés avec Firebase CLI
- [ ] Index créés dans Firebase Console (statut "Enabled")
- [ ] Application rechargée et testée
- [ ] Erreurs de console vérifiées (aucune erreur d'index)
- [ ] Notifications fonctionnelles
- [ ] Archivage fonctionnel
- [ ] Performances améliorées

---

**Date de mise à jour :** 2025-07-26
**Statut :** 🔄 Correction en cours
**Priorité :** 🔥 Critique - Bloque l'application
