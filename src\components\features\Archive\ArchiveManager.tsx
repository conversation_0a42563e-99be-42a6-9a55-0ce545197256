import React, { useState } from 'react';
import { useArchive } from '@/hooks/useArchive';
import { ArchiveData } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/common/Spinner';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { BackgroundWrapper } from '../../common/BackgroundWrapper';

/**
 * Composant de gestion des archives annuelles
 * Permet de consulter et gérer l'archivage automatique des données
 */
const ArchiveManager: React.FC = () => {
  const {
    archives,
    stats,
    formattedStats,
    availableYears,
    isLoading,
    error,
    archiveYear,
    getArchive,
    clearError
  } = useArchive();

  const [selectedArchive, setSelectedArchive] = useState<ArchiveData | null>(null);
  const [isArchiving, setIsArchiving] = useState(false);



  /**
   * Gère l'archivage manuel d'une année
   */
  const handleArchiveYear = async (year: number) => {
    setIsArchiving(true);
    const success = await archiveYear(year);
    setIsArchiving(false);

    if (success) {
      // Archivage terminé avec succès
    }
  };

  /**
   * Affiche les détails d'une archive
   */
  const handleViewArchive = async (year: number) => {
    const archive = await getArchive(year);
    setSelectedArchive(archive);
  };

  /**
   * Ferme la vue détaillée d'une archive
   */
  const handleCloseArchiveView = () => {
    setSelectedArchive(null);
  };

  // Affichage de débogage si erreur
  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-64 space-y-4">
        <div className="text-red-400">❌ Erreur: {error}</div>
        <Button onClick={clearError} variant="outline">
          Réessayer
        </Button>
      </div>
    );
  }

  // Affichage de chargement
  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center h-64 space-y-4">
        <Spinner />
        <span className="text-white">Chargement des archives...</span>
      </div>
    );
  }

  return (
    <BackgroundWrapper backgroundKey="archives" overlayOpacity={0.3}>
      <div className="container mx-auto px-4 py-6 pt-20 space-y-6 max-w-7xl">

        {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Archives FloraSynth</h1>
          <p className="text-gray-400 mt-2">
            Gestion de l'archivage automatique avec accès Gemini pour l'auto-apprentissage
          </p>
        </div>
        <Badge variant="outline" className="text-green-400 border-green-400">
          🤖 Gemini Learning Enabled
        </Badge>
      </div>

      {/* Affichage des erreurs */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-500/10 border border-red-500 rounded-lg p-4"
        >
          <div className="flex justify-between items-center">
            <span className="text-red-400">{error}</span>
            <Button variant="ghost" size="sm" onClick={clearError}>
              ✕
            </Button>
          </div>
        </motion.div>
      )}

      {/* Statistiques */}
      {formattedStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-[#1c1a31] border-gray-700">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-white">{formattedStats.totalArchives}</div>
              <div className="text-sm text-gray-400">Archives totales</div>
            </CardContent>
          </Card>
          
          <Card className="bg-[#1c1a31] border-gray-700">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-400">{formattedStats.totalArchivedPlants}</div>
              <div className="text-sm text-gray-400">Plantes archivées</div>
            </CardContent>
          </Card>
          
          <Card className="bg-[#1c1a31] border-gray-700">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-400">{formattedStats.totalArchivedDiagnostics}</div>
              <div className="text-sm text-gray-400">Diagnostics archivés</div>
            </CardContent>
          </Card>
          
          <Card className="bg-[#1c1a31] border-gray-700">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-400">{formattedStats.archiveRange}</div>
              <div className="text-sm text-gray-400">Période couverte</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Archives existantes */}
      <Card className="bg-[#1c1a31] border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Archives existantes</CardTitle>
        </CardHeader>
        <CardContent>
          {archives.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-4">Aucune archive disponible</div>
              <p className="text-sm text-gray-500">
                L'archivage automatique se déclenche chaque 1er janvier pour l'année précédente
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {archives.map((archive) => (
                <motion.div
                  key={archive.year}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="bg-[#100f1c] rounded-lg p-4 border border-gray-600"
                >
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-white">{archive.year}</h3>
                    {archive.geminiAccessible && (
                      <Badge variant="outline" className="text-xs text-green-400 border-green-400">
                        🤖 Gemini
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Plantes:</span>
                      <span className="text-white">{archive.totalPlants}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Diagnostics:</span>
                      <span className="text-white">{archive.totalDiagnostics}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Archivé le:</span>
                      <span className="text-white">
                        {format(archive.archivedAt.toDate(), 'dd/MM/yyyy', { locale: fr })}
                      </span>
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full mt-3"
                    onClick={() => handleViewArchive(archive.year)}
                  >
                    Voir les détails
                  </Button>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Archivage manuel */}
      {availableYears.length > 0 && (
        <Card className="bg-[#1c1a31] border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Archivage manuel</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400 mb-4">
              Vous pouvez déclencher manuellement l'archivage pour les années suivantes :
            </p>
            <div className="flex flex-wrap gap-2">
              {availableYears.map((year) => (
                <Button
                  key={year}
                  variant="outline"
                  onClick={() => handleArchiveYear(year)}
                  disabled={isArchiving}
                  className="border-purple-500 text-purple-400 hover:bg-purple-500/10"
                >
                  {isArchiving ? <Spinner className="w-4 h-4 mr-2" /> : null}
                  Archiver {year}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Modal de détails d'archive */}
      <AnimatePresence>
        {selectedArchive && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
            onClick={handleCloseArchiveView}
          >
            <motion.div
              initial={{ scale: 0.9, y: -20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: -20 }}
              className="bg-[#1c1a31] rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-white">Archive {selectedArchive.year}</h2>
                <Button variant="ghost" size="sm" onClick={handleCloseArchiveView}>
                  ✕
                </Button>
              </div>

              <div className="space-y-6">
                {/* Résumé */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Résumé</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-[#100f1c] rounded-lg p-3">
                      <div className="text-2xl font-bold text-green-400">{selectedArchive.totalPlants}</div>
                      <div className="text-sm text-gray-400">Plantes archivées</div>
                    </div>
                    <div className="bg-[#100f1c] rounded-lg p-3">
                      <div className="text-2xl font-bold text-blue-400">{selectedArchive.totalDiagnostics}</div>
                      <div className="text-sm text-gray-400">Diagnostics archivés</div>
                    </div>
                  </div>
                </div>

                {/* Liste des plantes */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">Plantes archivées</h3>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {selectedArchive.plants.map((plant) => (
                      <div key={plant.id} className="bg-[#100f1c] rounded-lg p-3">
                        <div className="font-medium text-white">{plant.name}</div>
                        {plant.species && (
                          <div className="text-sm text-gray-400">{plant.species}</div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Informations Gemini */}
                <div className="bg-green-500/10 border border-green-500 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <span className="text-green-400 mr-2">🤖</span>
                    <span className="font-medium text-green-400">Accès Gemini activé</span>
                  </div>
                  <p className="text-sm text-gray-300">
                    Ces données sont accessibles à Google Gemini pour l'auto-apprentissage et l'amélioration 
                    des diagnostics futurs. Les informations personnelles sont anonymisées.
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      </div>
    </BackgroundWrapper>
  );
};

export default ArchiveManager;
