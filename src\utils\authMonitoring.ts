/**
 * Système de monitoring des performances d'authentification
 * 
 * Fonctionnalités :
 * - Mesure des temps de connexion/déconnexion
 * - Détection des échecs d'authentification
 * - Métriques de performance
 * - Alertes automatiques
 */

import { logger } from './logger';

// Types pour le monitoring
export interface AuthMetrics {
  loginAttempts: number;
  loginSuccesses: number;
  loginFailures: number;
  averageLoginTime: number;
  lastLoginTime?: number;
  sessionDuration?: number;
  errorCodes: Record<string, number>;
}

export interface AuthEvent {
  type: 'login_start' | 'login_success' | 'login_failure' | 'logout' | 'session_expired';
  timestamp: number;
  duration?: number;
  errorCode?: string;
  method?: string;
  userId?: string;
}

class AuthMonitoringService {
  private metrics: AuthMetrics;
  private events: AuthEvent[];
  private loginStartTime?: number;
  private sessionStartTime?: number;
  private readonly maxEvents = 100;

  constructor() {
    this.metrics = {
      loginAttempts: 0,
      loginSuccesses: 0,
      loginFailures: 0,
      averageLoginTime: 0,
      errorCodes: {}
    };
    this.events = [];
    this.loadFromStorage();
  }

  /**
   * Charge les métriques depuis le localStorage
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem('florasynth_auth_metrics');
      if (stored) {
        const data = JSON.parse(stored);
        this.metrics = { ...this.metrics, ...data.metrics };
        this.events = data.events || [];
      }
    } catch (error) {
      logger.warn('Erreur lors du chargement des métriques d\'authentification', {
        component: 'AuthMonitoring',
        action: 'LoadFromStorage'
      }, error);
    }
  }

  /**
   * Sauvegarde les métriques dans le localStorage
   */
  private saveToStorage(): void {
    try {
      const data = {
        metrics: this.metrics,
        events: this.events.slice(-this.maxEvents) // Garder seulement les derniers événements
      };
      localStorage.setItem('florasynth_auth_metrics', JSON.stringify(data));
    } catch (error) {
      logger.warn('Erreur lors de la sauvegarde des métriques d\'authentification', {
        component: 'AuthMonitoring',
        action: 'SaveToStorage'
      }, error);
    }
  }

  /**
   * Enregistre le début d'une tentative de connexion
   */
  loginStart(method: string = 'Google'): void {
    this.loginStartTime = Date.now();
    this.metrics.loginAttempts++;

    const event: AuthEvent = {
      type: 'login_start',
      timestamp: this.loginStartTime,
      method
    };

    this.events.push(event);
    this.saveToStorage();

    logger.debug('Début de connexion enregistré', {
      component: 'AuthMonitoring',
      action: 'LoginStart'
    }, { method, attempts: this.metrics.loginAttempts });
  }

  /**
   * Enregistre une connexion réussie
   */
  loginSuccess(userId: string, method: string = 'Google'): void {
    const now = Date.now();
    const duration = this.loginStartTime ? now - this.loginStartTime : 0;

    this.metrics.loginSuccesses++;
    this.metrics.lastLoginTime = now;
    this.sessionStartTime = now;

    // Calcul de la moyenne des temps de connexion
    if (duration > 0) {
      const totalTime = this.metrics.averageLoginTime * (this.metrics.loginSuccesses - 1) + duration;
      this.metrics.averageLoginTime = Math.round(totalTime / this.metrics.loginSuccesses);
    }

    const event: AuthEvent = {
      type: 'login_success',
      timestamp: now,
      duration,
      method,
      userId: '[MASQUÉ]' // Ne pas stocker l'ID utilisateur
    };

    this.events.push(event);
    this.saveToStorage();

    logger.info('Connexion réussie enregistrée', {
      component: 'AuthMonitoring',
      action: 'LoginSuccess'
    }, {
      method,
      duration,
      successRate: this.getSuccessRate(),
      averageTime: this.metrics.averageLoginTime
    });

    // Alerte si le temps de connexion est anormalement long
    if (duration > 10000) { // Plus de 10 secondes
      logger.warn('Temps de connexion anormalement long détecté', {
        component: 'AuthMonitoring',
        action: 'SlowLogin'
      }, { duration, method });
    }
  }

  /**
   * Enregistre un échec de connexion
   */
  loginFailure(errorCode: string, method: string = 'Google'): void {
    const now = Date.now();
    const duration = this.loginStartTime ? now - this.loginStartTime : 0;

    this.metrics.loginFailures++;
    this.metrics.errorCodes[errorCode] = (this.metrics.errorCodes[errorCode] || 0) + 1;

    const event: AuthEvent = {
      type: 'login_failure',
      timestamp: now,
      duration,
      errorCode,
      method
    };

    this.events.push(event);
    this.saveToStorage();

    logger.error('Échec de connexion enregistré', {
      component: 'AuthMonitoring',
      action: 'LoginFailure'
    }, {
      errorCode,
      method,
      duration,
      failureRate: this.getFailureRate(),
      totalFailures: this.metrics.loginFailures
    });

    // Alerte si le taux d'échec devient trop élevé
    const failureRate = this.getFailureRate();
    if (failureRate > 0.3 && this.metrics.loginAttempts > 5) { // Plus de 30% d'échecs
      logger.critical('Taux d\'échec d\'authentification élevé détecté', {
        component: 'AuthMonitoring',
        action: 'HighFailureRate'
      }, {
        failureRate,
        totalAttempts: this.metrics.loginAttempts,
        totalFailures: this.metrics.loginFailures
      });
    }
  }

  /**
   * Enregistre une déconnexion
   */
  logout(): void {
    const now = Date.now();
    const sessionDuration = this.sessionStartTime ? now - this.sessionStartTime : 0;

    if (sessionDuration > 0) {
      this.metrics.sessionDuration = sessionDuration;
    }

    const event: AuthEvent = {
      type: 'logout',
      timestamp: now,
      duration: sessionDuration
    };

    this.events.push(event);
    this.saveToStorage();

    logger.info('Déconnexion enregistrée', {
      component: 'AuthMonitoring',
      action: 'Logout'
    }, {
      sessionDuration,
      sessionDurationMinutes: Math.round(sessionDuration / 60000)
    });
  }

  /**
   * Calcule le taux de succès des connexions
   */
  getSuccessRate(): number {
    if (this.metrics.loginAttempts === 0) return 0;
    return this.metrics.loginSuccesses / this.metrics.loginAttempts;
  }

  /**
   * Calcule le taux d'échec des connexions
   */
  getFailureRate(): number {
    if (this.metrics.loginAttempts === 0) return 0;
    return this.metrics.loginFailures / this.metrics.loginAttempts;
  }

  /**
   * Retourne les métriques actuelles
   */
  getMetrics(): AuthMetrics {
    return { ...this.metrics };
  }

  /**
   * Retourne les événements récents
   */
  getRecentEvents(limit: number = 20): AuthEvent[] {
    return this.events.slice(-limit);
  }

  /**
   * Génère un rapport de santé de l'authentification
   */
  getHealthReport(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
    metrics: AuthMetrics;
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // Vérifier le taux d'échec
    const failureRate = this.getFailureRate();
    if (failureRate > 0.5) {
      status = 'critical';
      issues.push(`Taux d'échec très élevé: ${Math.round(failureRate * 100)}%`);
      recommendations.push('Vérifier la configuration Firebase et les domaines autorisés');
    } else if (failureRate > 0.2) {
      status = 'warning';
      issues.push(`Taux d'échec élevé: ${Math.round(failureRate * 100)}%`);
      recommendations.push('Surveiller les erreurs d\'authentification');
    }

    // Vérifier le temps de connexion moyen
    if (this.metrics.averageLoginTime > 8000) {
      if (status !== 'critical') status = 'warning';
      issues.push(`Temps de connexion lent: ${Math.round(this.metrics.averageLoginTime / 1000)}s`);
      recommendations.push('Optimiser le processus d\'authentification');
    }

    // Vérifier les erreurs fréquentes
    const mostCommonError = Object.entries(this.metrics.errorCodes)
      .sort(([,a], [,b]) => b - a)[0];
    
    if (mostCommonError && mostCommonError[1] > 3) {
      if (status !== 'critical') status = 'warning';
      issues.push(`Erreur fréquente: ${mostCommonError[0]} (${mostCommonError[1]} fois)`);
      recommendations.push(`Investiguer l'erreur: ${mostCommonError[0]}`);
    }

    return {
      status,
      issues,
      recommendations,
      metrics: this.getMetrics()
    };
  }

  /**
   * Réinitialise les métriques
   */
  reset(): void {
    this.metrics = {
      loginAttempts: 0,
      loginSuccesses: 0,
      loginFailures: 0,
      averageLoginTime: 0,
      errorCodes: {}
    };
    this.events = [];
    this.saveToStorage();

    logger.info('Métriques d\'authentification réinitialisées', {
      component: 'AuthMonitoring',
      action: 'Reset'
    });
  }
}

// Instance singleton
export const authMonitoring = new AuthMonitoringService();

export default authMonitoring;
