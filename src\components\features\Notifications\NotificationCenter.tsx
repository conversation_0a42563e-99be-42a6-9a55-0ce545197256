import React, { useState } from 'react';
import { Bell, Calendar, Clock, AlertTriangle, CheckCircle, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs';
import { useNotifications, usePendingEvents, useNotificationStats } from '../../../hooks/useNotifications';
import { UserNotification, DiagnosticEvent } from '../../../types/notifications';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { getPriorityColors } from '../../../styles/colors';
import { BackgroundWrapper } from '../../common/BackgroundWrapper';

/**
 * Composant principal du centre de notifications
 */
export const NotificationCenter: React.FC = () => {
  const { notifications, unreadCount, markAsRead, deleteNotification } = useNotifications();
  const { todayEvents, overdueEvents, upcomingEvents } = usePendingEvents();
  const { stats } = useNotificationStats();
  const [activeTab, setActiveTab] = useState('notifications');

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors du marquage de la notification:', errorMessage);
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await deleteNotification(notificationId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la suppression de la notification:', errorMessage);
    }
  };

  return (
    <BackgroundWrapper backgroundKey="notifications" overlayOpacity={0.4}>
      <div className="max-w-4xl mx-auto p-6 pt-20 space-y-6">
        {/* En-tête avec statistiques */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="h-8 w-8 text-[#d385f5]" />
          <div>
            <h1 className="text-2xl font-bold text-white">Centre de Notifications</h1>
            <p className="text-[#E0E0E0]">Gérez vos rappels et suivez vos plantes</p>
          </div>
        </div>
        
        {stats && (
          <div className="flex gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-[#d385f5]">{stats.pendingEvents}</div>
              <div className="text-sm text-[#E0E0E0]">En attente</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-[#EF4444]">{stats.overdueEvents}</div>
              <div className="text-sm text-[#E0E0E0]">En retard</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-[#a364f7]">{unreadCount}</div>
              <div className="text-sm text-[#E0E0E0]">Non lues</div>
            </div>
          </div>
        )}
      </div>

      {/* Onglets principaux */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-1">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="events" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Événements
          </TabsTrigger>
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Tableau de bord
          </TabsTrigger>
        </TabsList>

        {/* Onglet Notifications */}
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications récentes
              </CardTitle>
            </CardHeader>
            <CardContent>
              {notifications.length === 0 ? (
                <div className="text-center py-8 text-[#E0E0E0]">
                  <Bell className="h-12 w-12 mx-auto mb-4 opacity-50 text-[#d385f5]" />
                  <p>Aucune notification pour le moment</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {notifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={handleMarkAsRead}
                      onDelete={handleDeleteNotification}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Événements */}
        <TabsContent value="events" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Événements d'aujourd'hui */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#3B82F6]">
                  <Calendar className="h-5 w-5" />
                  Aujourd'hui ({todayEvents.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {todayEvents.length === 0 ? (
                  <p className="text-[#9CA3AF] text-center py-4">Aucun événement aujourd'hui</p>
                ) : (
                  <div className="space-y-2">
                    {todayEvents.map((event) => (
                      <EventItem key={event.id} event={event} />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Événements en retard */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#EF4444]">
                  <AlertTriangle className="h-5 w-5" />
                  En retard ({overdueEvents.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {overdueEvents.length === 0 ? (
                  <p className="text-[#9CA3AF] text-center py-4">Aucun événement en retard</p>
                ) : (
                  <div className="space-y-2">
                    {overdueEvents.map((event) => (
                      <EventItem key={event.id} event={event} isOverdue />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Événements à venir */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-[#10B981]">
                <Clock className="h-5 w-5" />
                À venir cette semaine ({upcomingEvents.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {upcomingEvents.length === 0 ? (
                <p className="text-[#9CA3AF] text-center py-4">Aucun événement prévu cette semaine</p>
              ) : (
                <div className="grid gap-2 md:grid-cols-2">
                  {upcomingEvents.map((event) => (
                    <EventItem key={event.id} event={event} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Tableau de bord */}
        <TabsContent value="dashboard" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-[#E0E0E0]">Total événements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{stats?.pendingEvents || 0}</div>
                <p className="text-xs text-[#9CA3AF]">événements actifs</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-[#E0E0E0]">Notifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{stats?.totalNotifications || 0}</div>
                <p className="text-xs text-[#9CA3AF]">{unreadCount} non lues</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-[#E0E0E0]">En retard</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-[#EF4444]">{stats?.overdueEvents || 0}</div>
                <p className="text-xs text-[#9CA3AF]">nécessitent attention</p>
              </CardContent>
            </Card>
          </div>

          {/* Répartition par priorité */}
          {stats && (
            <Card>
              <CardHeader>
                <CardTitle>Répartition par priorité</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-[#EF4444]">
                      {stats.notificationsByPriority.urgent || 0}
                    </div>
                    <div className="text-sm text-[#E0E0E0]">Urgent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-[#d385f5]">
                      {stats.notificationsByPriority.high || 0}
                    </div>
                    <div className="text-sm text-[#E0E0E0]">Élevée</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-[#a364f7]">
                      {stats.notificationsByPriority.medium || 0}
                    </div>
                    <div className="text-sm text-[#E0E0E0]">Moyenne</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-[#10B981]">
                      {stats.notificationsByPriority.low || 0}
                    </div>
                    <div className="text-sm text-[#E0E0E0]">Faible</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
      </div>
    </BackgroundWrapper>
  );
};

/**
 * Composant pour afficher une notification individuelle
 */
interface NotificationItemProps {
  notification: UserNotification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onMarkAsRead, onDelete }) => {
  const getPriorityColor = (priority: string) => {
    const colors = getPriorityColors(priority as 'urgent' | 'high' | 'medium' | 'low');
    if (!colors) {
      return 'border-l-gray-500 bg-[#1c1a31]';
    }
    return `border-l-4 border-l-[${colors.border}] bg-[${colors.background}]`;
  };

  return (
    <div className={`p-4 rounded-lg ${getPriorityColor(notification.priority)} ${!notification.read ? 'shadow-md' : 'opacity-75'}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-white">{notification.title}</h4>
            {!notification.read && (
              <Badge variant="secondary" className="text-xs bg-[#d385f5] text-white">Nouveau</Badge>
            )}
          </div>
          <p className="text-[#E0E0E0] text-sm mb-2">{notification.message}</p>
          <p className="text-xs text-[#9CA3AF]">
            {formatDistanceToNow(notification.createdAt.toDate(), { addSuffix: true, locale: fr })}
          </p>
        </div>
        <div className="flex items-center gap-1 ml-2">
          {!notification.read && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMarkAsRead(notification.id)}
              className="text-[#d385f5] hover:bg-[#2a2847]"
              title="Marquer comme lu"
            >
              <CheckCircle className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(notification.id)}
            className="text-red-400 hover:bg-red-900/20 hover:text-red-300"
            title="Supprimer la notification"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * Composant pour afficher un événement individuel
 */
interface EventItemProps {
  event: DiagnosticEvent;
  isOverdue?: boolean;
}

const EventItem: React.FC<EventItemProps> = ({ event, isOverdue = false }) => {
  return (
    <div className={`p-3 rounded-lg border ${isOverdue ? 'border-red-500 bg-[#7F1D1D]' : 'border-[#374151] bg-[#1c1a31]'}`}>
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-medium text-white text-sm">{event.title}</h4>
        <Badge variant={isOverdue ? 'destructive' : 'secondary'} className="text-xs bg-[#d385f5] text-white">
          {event.priority}
        </Badge>
      </div>
      <p className="text-[#E0E0E0] text-xs mb-2">{event.plantName}</p>
      <p className="text-[#E0E0E0] text-sm">{event.nextActionType}</p>
      <p className="text-xs text-[#9CA3AF] mt-1">
        {event.nextActionDate.toDate().toLocaleDateString('fr-FR')}
      </p>
    </div>
  );
};

export default NotificationCenter;
