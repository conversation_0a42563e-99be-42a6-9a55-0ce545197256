# **Base de Connaissances "Violet Rikita" : <PERSON>tre<PERSON>, Guérison et Sauvetage des Plantes et Arbustes**

À l'attention d'Auggies, Assistant de Codage

## **Introduction**

Le présent document constitue une base de connaissances exhaustive destinée à alimenter la base de données de l'application "Violet Rikita". Son objectif est de fournir des informations précises et approfondies sur l'entretien, la nutrition, le diagnostic des maladies, ainsi que les stratégies de guérison et de sauvetage des plantes et arbustes. Une attention particulière est portée à la structuration des données pour faciliter leur intégration et leur utilisation par l'application.

L'élaboration de "Violet Rikita" repose sur des principes de développement rigoureux, incluant une architecture logicielle épurée, une gestion méticuleuse des fichiers et le respect des bonnes pratiques de codage. Ces éléments sont cruciaux pour garantir la robustesse, la maintenabilité et l'évolutivité de l'application. Ce document sert non seulement de référentiel botanique mais aussi de guide pour certaines conventions de développement à observer.

## **Section 1 : Amélioration du Workflow et Architecture du Projet "Violet Rikita"**

L'objectif principal du projet "Violet Rikita" est de développer une application web interactive et visuellement attrayante pour le soin des plantes, construite sur une architecture moderne et évolutive.1 Pour atteindre cet objectif, une attention particulière doit être portée à la propreté de l'architecture et à la rigueur du workflow de développement.

### **1.1 Principes d'une Architecture Projet Optimisée**

Une architecture "ultra propre", inspirée des standards de développement exigeants, repose sur plusieurs piliers :

* Modularité : Décomposer l'application en modules indépendants et réutilisables, chacun ayant une responsabilité clairement définie.  
* Séparation des préoccupations (SoC) : Isoler distinctement la logique de présentation (UI), la logique métier (application) et la logique d'accès aux données.  
* Clarté et Lisibilité : Adopter des conventions de nommage cohérentes et une organisation des fichiers intuitive pour faciliter la compréhension et la maintenance du code.  
* Testabilité : Concevoir le code de manière à faciliter l'écriture de tests unitaires et d'intégration.  
* Documentation : Maintenir une documentation à jour, tant au niveau du code (commentaires pertinents) qu'au niveau de l'architecture globale.

### **1.2 Structure des Dossiers du Projet (Rappel et Précisions)**

La structure de dossiers initialement proposée dans le workflow 1 constitue une base solide. Il est impératif de s'y conformer strictement :

/violet-rikita

|-- /dist                   \# Dossier de build final

|-- /public                 \# Contient les ressources statiques copiées telles quelles (favicon, manifest.json, etc.)

|-- /src                    \# Code source de l'application

| |-- /assets             \# Ressources traitées par le build (images, icônes SVG, polices)

| | |-- /images         \# Logos, fonds d'écran, illustrations spécifiques à l'app

| | |-- /icons          \# Icônes SVG pour l'UI

| | |-- /fonts          \# Polices personnalisées

| |-- /components         \# Composants UI réutilisables (ex: Card.js, Button.js, Modal.js)

| |-- /config             \# Fichiers de configuration spécifiques à l'application (ex: apiEndpoints.js)

| |-- /constants          \# Constantes utilisées à travers l'application (ex: errorMessages.js, eventTypes.js)

| |-- /firebase           \# Modules liés à Firebase (regroupement des anciens /lib/firebase.js, auth.js, database.js, storage.js)

| | |-- firebase.js     \# Config et initialisation de Firebase

| | |-- auth.js         \# Logique d'authentification

| | |-- database.js     \# Fonctions Firestore (CRUD)

| | |-- storage.js      \# Fonctions Cloud Storage

| |-- /services           \# Logique pour interagir avec des APIs externes ou services complexes

| | |-- GeminiService.js \# Logique pour appeler la Cloud Function d'analyse

| | |-- CalendarService.js\# Logique pour interagir avec Google Calendar API

| |-- /store              \# Gestion de l'état global si Alpine.js seul devient insuffisant (pourrait contenir des stores Pinia/Vuex-like si évolution)

| |-- /styles             \# Fichiers de style globaux et spécifiques

| | |-- main.css        \# Fichier CSS principal avec les directives Tailwind

| | |-- \_base.css       \# Styles de base, reset, variables CSS globales

| | |-- \_utilities.css  \# Classes utilitaires Tailwind personnalisées

| |-- /utils              \# Fonctions utilitaires génériques (ex: dateFormatting.js, validators.js)

| |-- /views (ou /pages)  \# Composants représentant les différentes pages/vues de l'application

| | |-- HomeView.js

| | |-- PlantDetailView.js

| |-- main.js             \# Point d'entrée principal (init Alpine.js, etc.)

|-- /functions              \# Dossier pour les Cloud Functions Firebase

| |-- /diagnosePlant

| |-- index.js

| |-- package.json

|-- /.vscode                \# Paramètres spécifiques à VSCode (optionnel, pour harmoniser l'environnement dev)

| |-- settings.json

|-- /\_tests\_                \# Dossier racine pour tous les fichiers de test (unitaires, intégration, e2e)

| |-- /unit

| |-- /integration

|-- /\_docs\_                 \# Dossier pour la documentation du projet (architecture, décisions de conception, etc.)

| |-- /adr                \# Architecture Decision Records

| |-- /guides             \# Guides de contribution, setup, etc.

|--.env.example            \# Exemple de fichier de variables d'environnement

|--.env.local              \# Variables d'environnement locales (doit être dans.gitignore)

|--.eslintrc.js            \# Configuration ESLint

|--.gitignore

|--.prettierrc.js          \# Configuration Prettier

|-- index.html              \# Point d'entrée HTML

|-- package.json

|-- postcss.config.js

|-- README.md

|-- tailwind.config.js

|-- vite.config.js

Justifications des ajouts/modifications :

* /public : Standard Vite.js pour les assets statiques non processés.  
* /src/components : Pour une meilleure organisation des éléments d'UI réutilisables.  
* /src/config, /src/constants, /src/utils, /src/views (ou /pages) : Structure plus granulaire pour une meilleure séparation des préoccupations.  
* /src/firebase : Regroupement logique des modules Firebase.  
* /\_tests\_ : Un dossier racine unique pour tous les tests améliore la clarté.  
* /\_docs\_ : Essentiel pour la maintenabilité à long terme, notamment les ADR (Architecture Decision Records).  
* Fichiers de configuration pour ESLint et Prettier : Pour assurer la qualité et la cohérence du code.  
* .env.example et .env.local : Gestion sécurisée des variables d'environnement.

### **1.3 Consignes Impératives pour l'Assistant de Codage (Auggies)**

Pour maintenir une propreté et une organisation irréprochables du projet, les directives suivantes sont à respecter scrupuleusement :

1. Gestion des Fichiers Temporaires et de Test :

   * Aucun fichier script ou texte de test ne doit être laissé à l'abandon à la racine du projet ou dans des emplacements non prévus. Cela inclut les brouillons, les expérimentations rapides, ou les fichiers de log temporaires.  
   * Si la création de fichiers de test est nécessaire pour des vérifications ponctuelles, un dossier dédié nommé /\_temp\_tests\_ (ou un nom similaire clairement identifiable et unique) doit être créé à la racine du projet.  
   * Tous les fichiers et le dossier /\_temp\_tests\_ lui-même doivent impérativement être supprimés une fois les tests terminés et avant tout commit. Ce dossier doit être ajouté au fichier .gitignore pour éviter toute inclusion accidentelle dans le versionnement.  
   * Pour les tests structurés (unitaires, intégration), utiliser le dossier /\_tests\_ prévu à cet effet.  
2. Gestion des Fichiers Markdown :

   * L'assistant apprécie la création de fichiers Markdown pour la documentation, les notes de développement, les listes d'améliorations ou les corrections.  
   * Tous ces fichiers Markdown doivent être centralisés dans le dossier /\_docs\_.  
   * Des sous-dossiers peuvent être créés au sein de /\_docs\_ pour une meilleure organisation (ex: /\_docs\_/notes-developpement, /\_docs\_/ameliorations, /\_docs\_/bugs-corriges).  
   * Les Architecture Decision Records (ADR) ont leur propre sous-dossier /\_docs\_/adr.  
3. Interdiction de Création de Fichiers Non Autorisés :

   * Aucun fichier (script, texte, configuration, etc.) ne doit être créé en dehors de la structure de dossiers définie sans une discussion et une autorisation préalables.  
   * L'objectif est d'éviter la prolifération de fichiers désorganisés qui complexifient la maintenance et la compréhension du projet.

Le respect de ces consignes est fondamental pour assurer la pérennité et la qualité du projet "Violet Rikita".

## **Section 2 : Charte Graphique Visuelle de l'Application "Violet Rikita"**

Une charte graphique cohérente et esthétiquement plaisante est essentielle pour offrir une expérience utilisateur de qualité et renforcer l'identité de l'application "Violet Rikita". L'objectif est de créer une interface "visuellement attrayante" 1, moderne et professionnelle, inspirée par les éléments visuels des maquettes de référence (Fluxco template) \[Images 2, 3, 4, 5, 6\].

### **2.1 Analyse des Références Visuelles et Principes Directeurs**

Les images de référence \[Images 2, 3, 4, 5, 6\] présentent une esthétique sombre, élégante et technologique, avec des touches de couleurs vives pour les appels à l'action et les éléments importants. Cette approche sera adoptée pour "Violet Rikita", en mettant l'accent sur :

* Contraste et Lisibilité : Malgré le thème sombre, une attention particulière sera portée aux contrastes pour assurer une excellente lisibilité des textes et une distinction claire des éléments interactifs.  
* Modernité et Professionnalisme : L'interface doit inspirer confiance et refléter l'expertise botanique de l'application.  
* Fluidité et Interactivité : L'utilisation de GSAP pour les animations 1 contribuera à une expérience utilisateur dynamique et engageante, sans être distrayante.

### **2.2 Palette de Couleurs**

La palette de couleurs s'inspire directement des teintes dominantes et des accents observés dans les images de référence.

| Utilisation de l'Élément d'Interface | Nom Descriptif de la Couleur | Code Hexadécimal | Notes/Justification |
| :---- | :---- | :---- | :---- |
| Arrière-plan Principal | Indigo Profond | \#1A1A2E | Couleur de fond dominante, sombre et sophistiquée, observée dans les arrière-plans généraux \[Images 2, 3, 5\]. |
| Arrière-plan Secondaire / Fond des Cartes | Gris Pourpre Foncé | \#2C2A4A | Teinte légèrement plus claire pour les conteneurs de contenu comme les cartes \[Images 2, 3, 5, 6\]. |
| Couleur d'Accent Primaire (CTA, Surlignages) | Magenta Vibrant | \#C846C6 | Couleur vive pour les boutons d'action principaux et les éléments à mettre en évidence \[Images 2, 3, 6\]. |
| Couleur d'Accent Secondaire (Optionnel) | Violet Moyen | \#7B68EE | Pourrait être utilisé pour des actions secondaires ou des icônes, offrant une variation douce. |
| Texte des Titres (H1, H2, H3) | Blanc Lavande | \#F0F0FF | Assure un contraste élevé et une bonne lisibilité sur fond sombre. |
| Texte du Corps | Gris Clair | \#D1D1E0 | Confortable pour la lecture de textes plus longs, tout en maintenant un bon contraste. |
| Bordures / Séparateurs Subtils | Gris Foncé | \#3D3B5E | Pour délimiter des sections ou des éléments sans être trop intrusif. |
| Couleur de Succès / Confirmation | Vert Doux | \#5CB85C | Couleur standard pour les messages de succès, adaptée au thème. |
| Couleur d'Avertissement / Erreur | Orange Foncé / Rouge Doux | \#F0AD4E / \#D9534F | Couleurs standards pour les alertes, adaptées au thème. |

### **2.3 Typographie**

La typographie jouera un rôle crucial dans la clarté et l'esthétique de l'application.

* Polices de Titres (H1, H2, H3) :  
  * Police : Montserrat ou Poppins (Sans-serif moderne et épuré, similaire à l'esprit des références \[Images 2, 3, 4, 5, 6\]).  
  * Graisse : H1, H2 : Bold (700) ; H3 : Semi-Bold (600).  
  * Couleur : Blanc Lavande (\#F0F0FF).  
* Police du Corps de Texte :  
  * Police : Open Sans ou Lato (Sans-serif très lisible, complémentaire aux titres).  
  * Graisse : Regular (400).  
  * Couleur : Gris Clair (\#D1D1E0).  
* Police des Éléments d'Interface (Boutons, Étiquettes) :  
  * Police : Open Sans ou Lato.  
  * Graisse : Medium (500) ou Semi-Bold (600) pour le texte des boutons.  
  * Couleur : Variable selon l'élément (ex: Blanc Lavande pour les boutons avec fond Magenta Vibrant).

### **2.4 Principes de Mise en Page et Style des Éléments d'Interface**

L'organisation visuelle et le style des composants sont essentiels pour une navigation intuitive.

* Mise en Page Générale :

  * Grille : Utilisation d'un système de grille (ex: 12 colonnes) pour un alignement cohérent des éléments.  
  * Espacement (Whitespace) : Utilisation généreuse des espaces vides pour aérer le contenu et améliorer la lisibilité, même sur un thème sombre. Les marges et paddings seront constants.  
  * Hiérarchie Visuelle : Différenciation claire des niveaux d'information par la taille, la graisse, la couleur de la typographie et le positionnement des éléments.  
  * Responsive Design : L'application doit être "100% Responsive Design and Mobile Friendly" \[Image 2\], s'adaptant à toutes les tailles d'écran.  
* Cartes (Cards) :

  * Élément central pour afficher les informations sur les plantes, les diagnostics, etc. (inspiration : sections "Our Latest Blog" \[Image 6\], "Pricing Plan" \[Image 3\], "What We Offer" \[Image 4\]).  
  * Fond : Gris Pourpre Foncé (\#2C2A4A).  
  * Coins : Arrondis (ex: 8px-12px).  
  * Ombre/Bordure : Subtile bordure (1px, \#3D3B5E) ou une ombre portée très discrète pour les détacher légèrement de l'arrière-plan principal, si nécessaire. Éviter les ombres trop prononcées qui peuvent alourdir le design sombre.  
  * Contenu : Organisation claire du contenu à l'intérieur des cartes avec des titres, textes et images bien structurés.  
* Boutons (Buttons) :

  * Style : Coins arrondis (ex: 4px-8px).  
  * Boutons Primaires (CTA) : Fond solide Magenta Vibrant (\#C846C6), texte Blanc Lavande (\#F0F0FF). Effet de survol (hover) avec un léger assombrissement ou éclaircissement du magenta. (Voir "Select Plan" \[Image 3\], "Contact Us" \[Image 2\]).  
  * Boutons Secondaires : Style "Ghost" (bordure Magenta Vibrant, fond transparent, texte Magenta Vibrant) ou fond Gris Pourpre Foncé avec texte Blanc Lavande.  
  * Hauteur et Padding : Dimensions confortables pour le clic/toucher.  
* Formulaires (Forms) :

  * Champs de Saisie : Fond Gris Pourpre Foncé (\#2C2A4A) ou légèrement plus clair, bordure subtile (\#3D3B5E), coins arrondis. Texte de saisie Gris Clair (\#D1D1E0).  
  * Étiquettes (Labels) : Claires, positionnées au-dessus des champs. Couleur Gris Clair (\#D1D1E0).  
  * États : Indication visuelle claire pour les états : focus (ex: bordure Magenta Vibrant), erreur (ex: bordure Rouge Doux).  
* Icônes :

  * Style : Minimaliste, type "line-art" ou icônes pleines simples et modernes. SVG privilégié pour la scalabilité et la personnalisation des couleurs. Les icônes thématiques de la section "What We Offer" \[Image 4\] sont un bon exemple de simplicité.  
  * Couleur : Blanc Lavande (\#F0F0FF) ou Magenta Vibrant (\#C846C6) selon le contexte.  
* Animations et Transitions :

  * Utilisation de GSAP 1 pour des animations fluides et significatives : transitions de page, apparition d'éléments, feedback interactif.  
  * Les animations doivent améliorer l'expérience sans être envahissantes, en particulier pour le parcours post-diagnostic qui doit être "amical et pédagogue".1

### **2.5 Style des Images et Illustrations**

* Photographies de Plantes : Haute qualité, claires, bien éclairées, mettant en valeur la plante ou le symptôme.  
* Illustrations : Si des illustrations personnalisées sont utilisées (ex: pour la visualisation du dosage 1), elles devront suivre l'esthétique moderne et épurée de l'application, avec des couleurs cohérentes avec la palette.

### **2.6 Inspiration pour les Écrans de l'Application**

* Tableau de Bord / Liste des Plantes : Une grille de cartes, similaire à la section "Our Latest Blog" \[Image 6\] ou "What We Offer" \[Image 4\], chaque carte représentant une plante avec une image et des informations clés.  
* Page de Détail d'une Plante : Pourrait comporter une image principale de la plante, des statistiques de soin (comme les chiffres d'accomplissement sur les références \[Images 2, 5\]), et des sections (potentiellement sous forme d'onglets ou d'accordéons) pour les informations de soin, l'historique des diagnostics, etc.  
* Flux de Diagnostic : Un processus étape par étape, potentiellement utilisant des modales ou des vues séquentielles, avec des appels à l'action clairs (similaire à la sélection des plans tarifaires \[Image 3\]). La présentation interactive du diagnostic (Verdict, Plan d'Action, Suivi) 1 devra être particulièrement soignée.

Le respect de cette charte graphique par l'assistant de codage est crucial pour assurer une identité visuelle forte et une expérience utilisateur optimale pour "Violet Rikita".

## **Section 3 : Intégration et Sécurité de l'API Google Gemini**

L'application "Violet Rikita" utilisera l'API Google Gemini (via Google AI Studio) pour sa fonctionnalité d'analyse et de diagnostic des plantes.1 L'intégration de cette API doit être réalisée avec une attention particulière portée à la sécurité de la clé API.

### **3.1 Utilisation de l'API Google AI Studio (Gemini)**

La Cloud Function diagnosePlant sera responsable des appels à l'API Gemini.1 Le prompt multimodal devra être soigneusement construit pour obtenir des réponses précises et formatées en JSON, comme spécifié dans le workflow.

### **3.2 Consignes de Sécurité Impératives pour la Clé API**

La clé API Google Gemini est une information sensible. Son exposition pourrait entraîner une utilisation non autorisée et des coûts imprévus. Les mesures suivantes sont impératives :

1. Non-Exposition dans le Code Client ou Versionné :

   * La clé API ne doit JAMAIS être codée en dur dans les fichiers JavaScript côté client (ex: main.js, GeminiService.js s'il était exécuté côté client) ni dans aucun fichier versionné par Git (poussé sur GitHub).  
   * Les fichiers de configuration locaux contenant la clé (ex: .env.local) doivent impérativement être listés dans le fichier .gitignore pour éviter leur commit.  
2. Stockage Sécurisé de la Clé API :

   * Côté Backend (Cloud Functions Firebase) : La méthode privilégiée pour stocker la clé API est d'utiliser les variables d'environnement configurées directement au niveau de l'environnement d'exécution des Cloud Functions Firebase.  
     * Lors du déploiement des fonctions, la clé API peut être définie via la console Firebase ou l'interface en ligne de commande Firebase (CLI) : firebase functions:config:set gemini.key="VOTRE\_CLE\_API\_ICI"  
     * Dans le code de la Cloud Function (functions/diagnosePlant/index.js), la clé sera accessible via functions.config().gemini.key.  
   * Fichier .env.local pour le développement local (hors Cloud Functions) : Si des tests de l'API Gemini sont effectués localement en dehors de l'émulateur Firebase Functions, un fichier .env.local (ignoré par Git) peut contenir la clé pour cet environnement de développement spécifique. Cependant, le code de production (Cloud Function) ne doit pas lire ce fichier.

### **3.3 Recommandation pour le Choix du Plan API**

Il est recommandé d'explorer les options de plans API proposées par Google AI Studio :

* Plan Gratuit (Free Tier) : Vérifier les limites du niveau gratuit. Si les quotas sont suffisants pour les phases de développement, de test et potentiellement pour un lancement initial avec un nombre limité d'utilisateurs, c'est l'option la plus économique.  
* Plans Payants "Généreux" : Si le niveau gratuit est insuffisant, évaluer les plans payants en fonction de l'utilisation estimée. Rechercher des options qui offrent un bon rapport volume d'appels / coût.  
* Surveillance des Coûts : Mettre en place des alertes de facturation dans la console Google Cloud Platform pour surveiller la consommation de l'API et éviter les dépassements de budget.

La sécurisation de la clé API est une responsabilité critique de l'assistant de codage. Toute négligence à ce sujet pourrait compromettre la sécurité et la viabilité financière du projet.

## **Section 4 : Base de Données Approfondie sur les Soins des Plantes**

Cette section détaille les informations essentielles concernant la nutrition des plantes, les engrais, ainsi que l'identification et le traitement des maladies et carences. Ces données formeront le cœur de la base de connaissances de "Violet Rikita".

### **4.1 Principes Fondamentaux de la Nutrition des Plantes**

Les plantes, pour leur croissance et leur développement optimal, requièrent un ensemble d'éléments nutritifs puisés principalement dans le sol. Ces éléments sont classiquement divisés en macronutriments et micronutriments (ou oligo-éléments).

* Macronutriments : Nécessaires en grandes quantités.  
  * Primaires : Azote (N), Phosphore (P), Potassium (K).  
  * Secondaires : Calcium (Ca), Magnésium (Mg), Soufre (S).  
* Micronutriments (Oligo-éléments) : Nécessaires en faibles quantités, mais tout aussi essentiels. Leur carence ou leur excès peut entraîner des désordres métaboliques significatifs.2 Ils incluent le Fer (Fe), Manganèse (Mn), Zinc (Zn), Cuivre (Cu), Bore (B), Molybdène (Mo), Chlore (Cl), et Nickel (Ni).

Une bonne terre de jardin, régulièrement amendée avec du compost, limite généralement les carences en oligo-éléments.3

### **4.2 Engrais : Composition, Utilisation et Recommandations**

Les engrais sont des substances apportées aux plantes pour améliorer leur croissance et leur productivité en fournissant des éléments nutritifs. Ils peuvent être organiques (d'origine animale ou végétale) ou minéraux (issus de l'industrie chimique ou de roches). La notation NPK indique les proportions d'Azote (N), de Phosphore (P₂O₅) et de Potassium (K₂O).

La base de données engrais dans Firestore 1 sera enrichie avec les informations suivantes, complétées par des recherches approfondies.

#### Liste Détaillée des Engrais :

L'objectif est de constituer une base de données extensible. Pour chaque engrais, les informations suivantes seront structurées :

* id (unique, ex: sulfate-de-fer)  
* nom (chaîne, ex: "Sulfate de Fer (II)")  
* nom\_scientifique (chaîne, optionnel, ex: FeSO4​⋅7H2​O)  
* type\_engrais (chaîne, ex: "Minéral", "Organique", "Organo-minéral")  
* composition\_npk (chaîne, ex: "0-0-0", "5-25-0", "Riche en Fe")  
* elements\_principaux (tableau de chaînes, ex:)  
* forme\_presentation (chaîne, ex: "Poudre", "Granulés", "Liquide")  
* description\_action (texte long : mode d'action, bénéfices pour la plante)  
* plantes\_cibles\_general (tableau de chaînes, ex:)  
* symptomes\_carence\_corriges (tableau de chaînes, ex: \["Chlorose ferrique (jaunissement des jeunes feuilles, nervures vertes)", "Croissance ralentie"\])  
* frequence\_application\_jours (nombre, ex: 15, 180\)  
* dosages (tableau d'objets, chaque objet décrivant une méthode) :  
  * methode (chaîne, ex: "Arrosage au sol", "Pulvérisation foliaire", "Incorporation au sol")  
  * dose (chaîne, ex: "15-20 g/m²", "2-3 g / 1 L d'eau")  
  * instructions\_application (texte long : comment préparer, appliquer)  
* periode\_utilisation\_ideale (tableau de chaînes, ex: \["Printemps", "Automne", "Plantation", "Phase de croissance"\])  
* precautions\_emploi (texte long : risques de surdosage, incompatibilités, équipement de protection)  
* agriculture\_biologique\_compatible (booléen, true/false)  
* notes\_complementaires (texte long, optionnel)

Engrais à intégrer 1 :

1. Sulfate de Fer (II)

   * id: sulfate-de-fer  
   * nom: "Sulfate de Fer (II)" (Anti-chlorose)  
   * nom\_scientifique: FeSO4​⋅7H2​O  
   * type\_engrais: Minéral  
   * composition\_npk: Ne contient pas de NPK significatif en tant qu'engrais majeur, mais apporte Fer et Soufre.  
   * elements\_principaux:  
   * forme\_presentation: Poudre ou granulés  
   * description\_action: Lutte contre la chlorose ferrique (jaunissement des feuilles dû à une carence en fer). Le fer est essentiel à la formation de la chlorophylle.2 Acidifie légèrement le sol.  
   * plantes\_cibles\_general: Plantes acidophiles (hortensias, rhododendrons, azalées, camélias, bruyères), rosiers, arbres fruitiers, légumes, gazon (pour reverdir et contre la mousse).  
   * symptomes\_carence\_corriges:  
   * frequence\_application\_jours: 15 (pour traitement curatif), peut varier selon l'usage.  
   * dosages: 1  
     * methode: "Arrosage au sol", dose: "15-20 g/m²", instructions\_application: "Mélanger au sol lors du bêchage ou saupoudrer en surface, puis arroser abondamment. Peut être utilisé pour corriger le pH des sols trop calcaires."  
     * methode: "Pulvérisation foliaire", dose: "2-3 g / 1 L d'eau", instructions\_application: "Appliquer directement sur les feuilles jaunes. Éviter de traiter en plein soleil pour ne pas brûler les feuilles. Solution à utiliser rapidement après préparation."  
   * periode\_utilisation\_ideale:  
   * precautions\_emploi: "Ne pas appliquer sur les dalles ou terrasses (risque de taches de rouille indélébiles). Peut être irritant pour la peau et les yeux, porter des gants et des lunettes. Un surdosage peut brûler les racines et nuire aux micro-organismes du sol."  
   * agriculture\_biologique\_compatible: Oui, sous conditions (vérifier la réglementation en vigueur).  
   * notes\_complementaires: Le sulfate de fer est aussi utilisé pour lutter contre la mousse dans les gazons. Son action acidifiante contribue à rendre le fer plus disponible dans les sols calcaires.4  
2. Sulfate de Magnésium (Sel d'Epsom)

   * id: sulfate-de-magnesium  
   * nom: "Sulfate de Magnésium (Sel d'Epsom)"  
   * nom\_scientifique: MgSO4​⋅7H2​O  
   * type\_engrais: Minéral  
   * composition\_npk: "MgO: 16%, SO₃: 32%".5 NPK non applicable directement.  
   * elements\_principaux:  
   * forme\_presentation: Cristaux solubles  
   * description\_action: Apporte du magnésium, essentiel à la photosynthèse (composant de la chlorophylle) et à l'activation de nombreuses enzymes. Le magnésium aide à l'assimilation du phosphore, de l'azote et du soufre.5 Le soufre est également un nutriment important. Action rapide.5  
   * plantes\_cibles\_general:  
   * symptomes\_carence\_corriges:.5  
   * frequence\_application\_jours: 15  
   * dosages: 1  
     * methode: "Arrosage au sol", dose: "20 g / 10 L d'eau (ou 1-2 cuillères à soupe par 4L d'eau)", instructions\_application: "Arroser au pied des plantes. Répéter toutes les 2-4 semaines si nécessaire."  
     * methode: "Pulvérisation foliaire", dose: "1 c.à.c / 1 L d'eau", instructions\_application: "Vaporiser sur le feuillage, de préférence tôt le matin ou tard le soir. Répéter 1 à 2 fois par mois."  
   * periode\_utilisation\_ideale:  
   * precautions\_emploi: "Bien diagnostiquer la carence avant application pour éviter de brûler les plantes.5 Un excès de potassium ou de calcium dans le sol peut inhiber l'absorption du magnésium.5 Ne pas utiliser sur des sols déjà très riches en magnésium."  
   * agriculture\_biologique\_compatible: Oui.5  
3. Sulfate de Potassium

   * id: sulfate-de-potassium  
   * nom: "Sulfate de Potassium" (Potasse)  
   * nom\_scientifique: K2​SO4​  
   * type\_engrais: Minéral  
   * composition\_npk: "0-0-50" (50% K2​O).7 Contient aussi du soufre.  
   * elements\_principaux:  
   * forme\_presentation: Poudre ou granulés solubles  
   * description\_action: Apporte du potassium, crucial pour la floraison, la fructification, la résistance aux maladies, au gel et à la sécheresse. Améliore la qualité et la conservation des fruits et légumes.7 Le soufre est essentiel pour la synthèse des protéines et la fonction enzymatique.7 Ne contient pas de chlore, adapté aux plantes sensibles au chlore.7  
   * plantes\_cibles\_general:.7  
   * symptomes\_carence\_corriges:.6  
   * frequence\_application\_jours: 21 (peut varier, ex: tous les 15 jours jusqu'à 4 fois par saison 7)  
   * dosages:  
     * methode: "Arrosage au sol / Incorporation", dose: "20-40 g/m²" 1, ou "0.5-1 g par litre d'eau" pour arrosage.7  
     * instructions\_application: "Répartir uniformément et incorporer légèrement au sol si possible, puis arroser. Pour l'arrosage, dissoudre dans l'eau."  
   * periode\_utilisation\_ideale: \["Printemps (avant floraison)", "Été (pendant la fructification)", "Automne (pour renforcer avant l'hiver)"\]  
   * precautions\_emploi: "Compatible avec la plupart des engrais sauf ceux contenant du calcium (précipitation de sulfate de calcium).7 Un excès de potassium peut induire des carences en magnésium ou calcium."  
   * agriculture\_biologique\_compatible: Oui, certaines formulations.  
4. Urée (Engrais Azoté)

   * id: uree-azote  
   * nom: "Urée (Engrais Azoté)"  
   * nom\_scientifique: CO(NH2​)2​  
   * type\_engrais: Minéral (parfois classé organique de synthèse)  
   * composition\_npk: "46-0-0" (46% Azote).9  
   * elements\_principaux: \["Azote (N)"\]  
   * forme\_presentation: Granulés ou perlée, très soluble dans l'eau.  
   * description\_action: Fournit une forte concentration d'azote, essentiel pour la croissance végétative, le développement du feuillage et la couleur verte des plantes.9 L'azote uréique se transforme en azote ammoniacal puis nitrique dans le sol pour être absorbé.  
   * plantes\_cibles\_general: \["Légumes feuilles (salades, épinards)", "Gazon", "Grandes cultures (maïs, blé)", "Plantes en phase de croissance active nécessitant beaucoup d'azote"\].9  
   * symptomes\_carence\_corriges: \["Jaunissement généralisé des feuilles (en commençant par les plus anciennes)", "Croissance lente et chétive", "Feuillage clairsemé"\].6  
   * frequence\_application\_jours: 21 (variable selon les besoins)  
   * dosages: 1  
     * methode: "Arrosage au sol / Incorporation", dose: "20-30 g/m²" 1, ou "3-4 Kg / 100 m² pour cultures horticoles".9  
     * instructions\_application: "Répartir sur sol humide et incorporer légèrement si possible pour éviter la volatilisation de l'ammoniac. Arroser après application. Peut aussi être dissoute pour fertigation ou pulvérisation foliaire (avec précaution pour éviter brûlures)."  
   * periode\_utilisation\_ideale:  
   * precautions\_emploi: "Risque de brûlure des feuilles et des racines en cas de surdosage ou d'application sur feuillage sec. Ne pas appliquer par temps très chaud. Peut acidifier le sol à long terme. Éviter le contact direct des granulés avec les tiges ou les feuilles."  
   * agriculture\_biologique\_compatible: Non, généralement l'urée de synthèse n'est pas autorisée.  
5. Phosphate Naturel (Poudre d'os)

   * id: phosphate-naturel  
   * nom: "Phosphate Naturel (Poudre d'os)"  
   * type\_engrais: Organique  
   * composition\_npk: Variable, ex: "NPK 5-25-0" pour poudre d'os marine 10, "NPK 5,5-20-0".11 Principalement Phosphore (P) et Calcium (Ca), un peu d'Azote (N).  
   * elements\_principaux: \["Phosphore (P)", "Calcium (Ca)", "Azote (N)"\]  
   * forme\_presentation: Poudre ou granulés fins.  
   * description\_action: Source de phosphore à libération lente, favorise le développement racinaire, la floraison et la fructification. Le calcium contribue à la structure cellulaire. Stimule la vie microbienne du sol.10  
   * plantes\_cibles\_general:  
   * symptomes\_carence\_corriges:.6  
   * frequence\_application\_jours: 180 (engrais de fond)  
   * dosages: 1  
     * methode: "Incorporation au sol", dose: "50 g/m²" 1, ou "une poignée par trou de plantation".  
     * instructions\_application: "Incorporer à la terre lors de la plantation ou au printemps pour les plantes établies. Bien mélanger au sol au niveau des racines."  
   * periode\_utilisation\_ideale:  
   * precautions\_emploi: "Action lente, ne pas s'attendre à des résultats immédiats. Moins efficace en sols très alcalins (pH \> 7.5) car le phosphore devient moins disponible. Sans risque de brûlure.10"  
   * agriculture\_biologique\_compatible: Oui.10  
6. Purin d'Ortie

   * id: purin-ortie  
   * nom: "Purin d'ortie"  
   * type\_engrais: Organique (extrait fermenté de plante)  
   * composition\_npk: Riche en Azote (N), contient aussi Potassium (K), Fer (Fe), Calcium (Ca), Magnésium (Mg), oligo-éléments et vitamines.12 NPK variable.  
   * elements\_principaux: \["Azote (N)", "Potassium (K)", "Fer (Fe)", "Calcium (Ca)"\]  
   * forme\_presentation: Liquide (à diluer)  
   * description\_action: Stimule la croissance des plantes, renforce leurs défenses immunitaires, prévient certaines maladies fongiques et a un effet répulsif sur certains insectes (pucerons, acariens).12 Améliore la biodiversité du sol. Diminue la chlorose.12  
   * plantes\_cibles\_general:.12 Particulièrement bénéfique pour les légumes feuilles.  
   * symptomes\_carence\_corriges: \["Carences minérales générales", "Chlorose légère", "Manque de vigueur"\]  
   * frequence\_application\_jours: 15  
   * dosages: 1  
     * methode: "Arrosage (fertilisant)", dose: "Dilution 10% (1L de purin pour 9-10L d'eau)" 1, instructions\_application: "Arroser au pied des plantes tous les 10-15 jours pendant la période de croissance."  
     * methode: "Pulvérisation (répulsif/fortifiant)", dose: "Dilution 5% (0.5L de purin pour 9.5-10L d'eau)" 1, instructions\_application: "Pulvériser sur le feuillage (dessus et dessous des feuilles) tôt le matin ou tard le soir. Éviter en plein soleil ou par temps chaud et humide.12"  
   * periode\_utilisation\_ideale: \["Printemps", "Été (période de croissance)"\]  
   * precautions\_emploi: "Ne pas utiliser pur (risque de brûlure). Un surdosage peut attirer les pucerons ou favoriser les maladies.12 Odeur forte mais temporaire. Ne pas utiliser sur jeunes semis ou plants fraîchement repiqués (attendre 15-20 jours).12 Ne pas mélanger avec la bouillie bordelaise."  
   * agriculture\_biologique\_compatible: Oui.12  
   * notes\_complementaires: Peut aussi être utilisé comme activateur de compost.12 Recette de fabrication : 1 kg d'orties fraîches (ou 100-200g sèches) pour 10L d'eau de pluie, laisser fermenter 1-2 semaines en remuant quotidiennement, filtrer.13 Conservation : plusieurs mois à l'abri de la lumière et de l'air.14  
7. Amendement Calcaire (Coquilles d'huîtres broyées)

   * id: amendement-calcaire  
   * nom: "Amendement Calcaire (Coquilles d'huîtres broyées)"  
   * type\_engrais: Amendement organique  
   * composition\_npk: Principalement Carbonate de Calcium (CaCO3​, environ 95%), oligo-éléments (sodium, magnésium, soufre).15 NPK non applicable.  
   * elements\_principaux: \["Calcium (Ca)"\]  
   * forme\_presentation: Poudre ou fragments plus ou moins fins.  
   * description\_action: Apporte du calcium à libération lente, corrige l'acidité du sol (augmente le pH), améliore la structure du sol et la disponibilité des nutriments.15 Peut aussi servir de barrière physique contre les limaces et escargots.16  
   * plantes\_cibles\_general:  
   * symptomes\_carence\_corriges: \["Nécrose apicale des tomates ('cul noir')", "Problèmes liés à l'acidité du sol", "Mauvaise structure du sol"\]  
   * frequence\_application\_jours: 180 (ou plus, action lente et durable)  
   * dosages: 1  
     * methode: "Amendement de fond / Incorporation au sol", dose: "100 g/m²" 1 (peut varier de 100 à 500 g/m² selon l'acidité).  
     * instructions\_application: "Mélanger à la terre à l'automne ou au printemps. Pour une action plus rapide, broyer finement. Peut être ajouté au compost.15"  
   * periode\_utilisation\_ideale: \["Automne", "Printemps", "Préparation du sol avant plantation"\]  
   * precautions\_emploi: "Ne pas utiliser sur sols déjà calcaires ou pour plantes acidophiles (hortensias, rhododendrons). L'effet sur le pH est progressif."  
   * agriculture\_biologique\_compatible: Oui.  
8. Soufre Élémentaire (Fleur de soufre)

   * id: soufre-elementaire  
   * nom: "Soufre Élémentaire (Fleur de soufre / Soufre mouillable)"  
   * type\_engrais: Minéral (élément nutritif et fongicide/acaricide)  
   * composition\_npk: Contient du Soufre (S). NPK non applicable.  
   * elements\_principaux:  
   * forme\_presentation: Poudre très fine (fleur de soufre) ou micro-granulés (soufre mouillable).  
   * description\_action: Utilisé comme fongicide (contre oïdium, tavelure, rouille), acaricide (contre acariens) et pour acidifier les sols calcaires.17 Le soufre est un oligoélément indispensable à la croissance des plantes.17  
   * plantes\_cibles\_general:.17  
   * symptomes\_carence\_corriges:. Principalement utilisé pour ses propriétés fongicides/acaricides.  
   * frequence\_application\_jours: 90 (pour acidification), 7-14 jours (pour traitement fongicide/acaricide).  
   * dosages: 1  
     * methode: "Acidification du sol", dose: "30-50 g/m²" 1, instructions\_application: "Pour les plantes acidophiles. Incorporer légèrement au sol. Ne pas utiliser comme engrais direct."  
     * methode: "Action fongicide/acaricide (pulvérisation)", dose: "5g / 1 L d'eau (soufre mouillable)" 1, ou "7.5g à 12.5g pour 10m²".17  
     * instructions\_application: "Diluer le soufre mouillable dans l'eau et pulvériser sur les deux faces des feuilles. Appliquer par temps calme, sans vent, et à des températures entre 18°C et 25-28°C.17 Ne pas traiter par forte chaleur (\>28-30°C) ni en plein soleil (risque de brûlures). Ne pas mélanger avec des huiles."  
   * periode\_utilisation\_ideale: \["Printemps et été pour traitements fongicides/acaricides (éviter floraison)", "Automne/Printemps pour acidification"\]  
   * precautions\_emploi: "Irritant pour les yeux et les voies respiratoires, porter masque et lunettes.18 Toxique pour certains insectes auxiliaires.17 Peut s'accumuler dans le sol et l'acidifier excessivement avec des applications répétées.17 Arrêter les traitements 1 semaine avant récolte.17"  
   * agriculture\_biologique\_compatible: Oui.17  
9. Mélange d'Oligo-éléments

   * id: oligo-elements-mix  
   * nom: "Mélange d'oligo-éléments"  
   * type\_engrais: Minéral ou Organo-minéral  
   * composition\_npk: Généralement faible ou nul en NPK.  
   * elements\_principaux:.1  
   * forme\_presentation: Poudre soluble, granulés ou liquide.  
   * description\_action: Fournit un apport équilibré des principaux micronutriments essentiels à de nombreuses fonctions vitales de la plante (photosynthèse, formation des parois cellulaires, activation enzymatique, résistance au stress).2 Prévient et corrige les carences multiples.  
   * plantes\_cibles\_general:  
   * symptomes\_carence\_corriges: Symptômes variés selon l'élément manquant : déformations, chloroses, nanisme, mauvaise fructification.2  
   * frequence\_application\_jours: 180 (en préventif, 1 à 2 fois par an)  
   * dosages: 1  
     * methode: "Prévention des carences (arrosage au sol ou pulvérisation foliaire)", dose: "5-10 g / 10 L d'eau" (variable selon le produit).  
     * instructions\_application: "Arroser au pied des plantes ou pulvériser sur le feuillage selon les recommandations du produit spécifique. Une application annuelle ou bisannuelle est souvent suffisante en prévention."  
   * periode\_utilisation\_ideale:  
   * precautions\_emploi: "Respecter scrupuleusement les doses, un excès d'oligo-éléments peut être toxique. Certains oligo-éléments peuvent interagir entre eux."  
   * agriculture\_biologique\_compatible: Oui, pour les produits certifiés.  
10. Corne Broyée

    * id: corne-broyee  
    * nom: "Corne Broyée"  
    * type\_engrais: Organique (origine animale)  
    * composition\_npk: Principalement Azote (N), typiquement "13-0-0".20  
    * elements\_principaux: \["Azote (N)"\]  
    * forme\_presentation: Poudre ou granulés plus ou moins fins.  
    * description\_action: Engrais azoté à libération lente et progressive, nourrissant les plantes sur le long terme. Favorise la croissance végétative. Ne brûle pas les racines.20  
    * plantes\_cibles\_general: \["Arbres et arbustes à la plantation", "Plantes vivaces", "Légumes feuilles", "Engrais de fond"\]  
    * symptomes\_carence\_corriges: \["Manque de vigueur général", "Feuillage pâle (action lente)"\]  
    * frequence\_application\_jours: 180-365 (action longue durée)  
    * dosages:  
      * methode: "Incorporation au sol", dose: "50-75 g/m² pour légumes et fleurs ; 300-500g par arbre/arbuste à la plantation".20  
      * instructions\_application: "Mélanger à la terre de plantation ou incorporer superficiellement au pied des plantes établies, à l'automne ou au printemps."  
    * periode\_utilisation\_ideale: \["Plantation (automne/printemps)", "Entretien annuel (automne/printemps)"\]  
    * precautions\_emploi: "Effet lent. Un excès peut déséquilibrer la nutrition, notamment pour les légumes racines qui développeront plus de feuilles que de racines.20 Ne convient pas pour un effet 'coup de fouet'."  
    * agriculture\_biologique\_compatible: Oui.  
11. Sang Séché

    * id: sang-seche  
    * nom: "Sang Séché"  
    * type\_engrais: Organique (origine animale)  
    * composition\_npk: Riche en Azote (N), typiquement "12-0-0" à "14-0-0".  
    * elements\_principaux: \["Azote (N)"\]  
    * forme\_presentation: Poudre.  
    * description\_action: Engrais azoté "coup de fouet" à action rapide et durable. Stimule rapidement la croissance et le verdissement du feuillage. Ne brûle pas les racines si bien dosé.21  
    * plantes\_cibles\_general: \["Légumes feuilles (salades, épinards)", "Plantes en pot", "Gazon", "Plantes ayant besoin d'un apport rapide d'azote"\]  
    * symptomes\_carence\_corriges: \["Jaunissement du feuillage", "Croissance ralentie (effet rapide)"\]  
    * frequence\_application\_jours: 30-60 (selon besoins)  
    * dosages:  
      * methode: "Épandage et incorporation légère / Dilution pour arrosage", dose: "50 g/m² pour gazon ; 75 g/m² pour potager et massifs".21  
      * instructions\_application: "Épandre à la surface du sol et griffer légèrement pour incorporer. Arroser après application. Peut être dilué dans l'eau d'arrosage."  
    * periode\_utilisation\_ideale: \["Printemps (reprise de la végétation)", "Pendant la croissance active"\]  
    * precautions\_emploi: "Respecter les doses pour éviter un excès d'azote qui peut rendre les plantes plus sensibles aux maladies et pucerons."  
    * agriculture\_biologique\_compatible: Oui.  
12. Engrais NPK Équilibré (ex: 20-20-20)

    * id: npk-20-20-20  
    * nom: "Engrais NPK 20-20-20 (ou similaire équilibré)"  
    * type\_engrais: Minéral  
    * composition\_npk: "20-20-20" (contient des quantités égales d'azote, phosphore et potassium).22  
    * elements\_principaux: \["Azote (N)", "Phosphore (P)", "Potassium (K)"\]  
    * forme\_presentation: Poudre soluble ou granulés.  
    * description\_action: Fournit une nutrition équilibrée pour une croissance générale, le développement des racines, la floraison et la fructification. Polyvalent pour de nombreux types de plantes.22  
    * plantes\_cibles\_general:.22  
    * symptomes\_carence\_corriges: \["Carences multiples en N, P, K", "Croissance faible généralisée"\]  
    * frequence\_application\_jours: 15-30 (selon les plantes et la saison)  
    * dosages:  
      * methode: "Arrosage au sol / Fertigation", dose: "Variable selon le produit, ex: pour pulvérisation foliaire diluer 800-1200 fois ; pour fertigation 250kg/ha pour légumes".22  
      * instructions\_application: "Dissoudre dans l'eau selon les instructions du fabricant. Appliquer au pied des plantes ou en pulvérisation foliaire (avec précaution)."  
    * periode\_utilisation\_ideale: \["Période de croissance active (printemps, été)"\]  
    * precautions\_emploi: "Risque de sur-fertilisation élevé en raison de la concentration : accumulation de sels, brûlures racinaires, déséquilibres.23 Peut polluer les sols et cours d'eau en cas de sur-utilisation.23 Peut entraîner un appauvrissement des sols à long terme et une diminution de la biodiversité souterraine.23 Manipuler avec précaution (irritant, lire FDS).24"  
    * agriculture\_biologique\_compatible: Non (généralement synthétique).

#### Recherche de Bases de Données Externes :

Pour compléter et maintenir à jour la base de données des engrais et des soins, il est recommandé d'explorer et, si possible, d'établir des connexions (via API si elles existent, ou par scraping contrôlé et autorisé) avec des bases de données botaniques et horticoles reconnues. Quelques pistes :

* Bases de données académiques et institutionnelles : INRAE, Tela Botanica (pour l'identification des plantes, pourrait avoir des informations de soin associées).  
* Projets Open Source de jardinage : OpenFarm, FarmBot.  
* Sites spécialisés de jardinage : Gerbeaud, Rustica, Gamm Vert, Jardiland, etc., peuvent offrir des API ou des données structurées (à vérifier).  
* Bases de données de produits phytosanitaires et d'engrais homologués : E-phy (ANSES) pour les produits autorisés en France, bien que plus axé sur les produits professionnels.

L'objectif est de s'assurer que "Violet Rikita" dispose des informations les plus actuelles et complètes possibles.

### **4.3 Remèdes aux Maladies et Ravageurs**

L'identification précoce des symptômes est cruciale pour un traitement efficace.

#### 4.3.1 Identification des Symptômes Courants

Les plantes malades peuvent présenter une variété de signes 25 :

* Feuilles :  
  * Jaunissement (uniforme, internervaire, taches) 6  
  * Taches (brunes, noires, blanches, poudreuses, auréolées) 25  
  * Déformations (cloquage, enroulement, boursouflures) 25  
  * Perforations, grignotages 25  
  * Dessèchement, flétrissement 25  
  * Chute prématurée 26  
  * Présence de miellat, fumagine (suie noire) 25  
  * Présence de toiles fines (acariens) 25  
* Tiges et Rameaux :  
  * Chancres, lésions 29  
  * Pourriture 29  
  * Présence d'insectes (pucerons, cochenilles) 31  
* Fleurs et Fruits :  
  * Pourriture, moisissure 29  
  * Déformations, taches 30  
  * Chute prématurée  
* Racines :  
  * Pourriture (souvent due à un excès d'eau) 25  
  * Mauvais développement 29  
* Aspect Général :  
  * Croissance ralentie, nanisme 19  
  * Dépérissement

#### 4.3.2 Principales Maladies et Ravageurs (avec focus Auvergne-Rhône-Alpes si possible)

Pour chaque pathologie ou ravageur, la base de données devrait contenir :

* id\_maladie\_ravageur  
* nom\_commun  
* nom\_scientifique\_agent\_causal (ex: *Taphrina deformans* pour la cloque)  
* type\_probleme (ex: "Maladie fongique", "Maladie bactérienne", "Ravageur insecte", "Ravageur acarien")  
* description\_generale  
* symptomes\_visuels (tableau de chaînes, détaillant les signes sur feuilles, tiges, fruits, etc.)  
* plantes\_hotes\_frequentes (tableau de chaînes, ex:)  
* plantes\_specifiques\_aura (tableau de chaînes, si des données régionales existent 32)  
* conditions\_favorables\_developpement (texte : humidité, température, saison)  
* methodes\_prevention (tableau de chaînes : rotation cultures, espacement, taille, choix variétés résistantes, etc.)  
* traitements\_naturels\_biologiques (tableau d'objets) :  
  * nom\_traitement (ex: "Bouillie Bordelaise", "Purin d'ortie")  
  * type\_traitement (ex: "Fongicide", "Insecticide", "Répulsif")  
  * recette\_preparation (texte, si applicable)  
  * dosage\_application (texte)  
  * instructions\_utilisation (texte)  
  * frequence\_traitement (texte)  
  * precautions (texte)  
  * reference\_snippet (optionnel, pour traçabilité interne)  
* lutte\_biologique\_options (tableau d'objets, si applicable) :  
  * agent\_biologique (ex: "Coccinelles (Adalia bipunctata)", "Nématodes (Steinernema feltiae)")  
  * cible\_ravageur (ex: "Pucerons", "Larves de doryphores")  
  * mode\_action (texte)  
  * conditions\_utilisation (texte)  
  * reference\_snippet

Exemples de maladies et ravageurs à documenter :

* Maladies Fongiques :

  * Mildiou :  
    * Symptômes : Taches jaunâtres puis brunâtres sur les feuilles, duvet blanc au revers. Fruits tachés. 25  
    * Plantes : Vigne, tomate, pomme de terre, rosier, courges. 29  
    * Conditions : Temps chaud et humide. 38  
    * Prévention : Espacement, aération, éviter d'arroser le feuillage, rotation des cultures. 38  
    * Traitements : Bouillie bordelaise (préventif/curatif léger) 39, décoction de prêle (renforce la plante, asséchant) 39, bicarbonate de soude.  
  * Oïdium (Maladie du blanc) :  
    * Symptômes : Feutrage blanc poudreux sur feuilles, tiges, bourgeons. Feuilles déformées, desséchées. 25  
    * Plantes : Rosier, vigne, pommier, courgettes, tomate, chêne, érable. 25  
    * Conditions : Temps chaud et sec, forte humidité nocturne, confinement. 25  
    * Prévention : Bonne aération, éviter excès d'azote, taille. 30  
    * Traitements : Soufre (fleur de soufre ou soufre mouillable) 17, lait dilué 39, bicarbonate de soude 30, décoction de prêle.42  
  * Rouille :  
    * Symptômes : Pustules poudreuses orange, brunes ou noires sous les feuilles, taches jaunâtres sur le dessus. 28  
    * Plantes : Rosier, prunier, menthe, géranium, rose trémière, poirier. 28  
    * Conditions : Humidité élevée, chaleur modérée. 28  
    * Prévention : Ramasser feuilles mortes, bonne circulation d'air.  
    * Traitements : Décoction de prêle 31, purin d'ortie (fortifiant) 31, soufre.  
  * Tavelure :  
    * Symptômes : Taches noires ou brunes sur feuilles et fruits, craquelures, déformations. 30  
    * Plantes : Pommier, poirier. 30  
    * Conditions : Temps humide et doux au printemps. 30  
    * Prévention : Ramasser feuilles mortes, taille, variétés résistantes, bouillie bordelaise (automne/débourrement), décoction de prêle. 30  
    * Traitements : Soufre, décoction d'ail. 30  
  * Cloque du Pêcher :  
    * Symptômes : Feuilles cloquées, boursouflées, épaissies, rougissantes puis se desséchant. 30  
    * Plantes : Pêcher, nectarinier, abricotier. 30  
    * Conditions : Printemps frais et humide. Champignon hiverne sur l'écorce et les bourgeons. 47  
    * Prévention : Bouillie bordelaise (chute des feuilles et débourrement) 30, décoction de prêle, badigeon d'argile, purin d'ortie, variétés résistantes. 30  
    * Traitements : Retirer feuilles atteintes, bicarbonate de soude, décoction d'ail, soufre. 30  
  * Moniliose (Pourriture des fruits et fleurs) :  
    * Symptômes : Fleurs se dessèchent, fruits pourrissent sur l'arbre (coussinets blanchâtres ou grisâtres). 29  
    * Plantes : Arbres fruitiers à noyaux et à pépins (pêcher, prunier, cerisier, pommier, poirier). 29  
    * Prévention : Éliminer fruits momifiés et rameaux atteints, taille d'éclaircie, bouillie bordelaise. 40  
    * Traitements : Décoction de prêle.  
  * Botrytis (Pourriture grise) :  
    * Symptômes : Moisissure grise cotonneuse sur fleurs, fruits, tiges. 29  
    * Plantes : Fraisier, vigne, tomate, rosier, plantes en serre. 29  
    * Prévention : Bonne aération, éviter excès d'humidité, éliminer débris végétaux.  
    * Traitements : Purin de prêle, décoction d'ail. 31  
  * Fumagine :  
    * Symptômes : Dépôt noir semblable à de la suie sur les feuilles, tiges. Résulte du développement d'un champignon sur le miellat excrété par des insectes piqueurs (pucerons, cochenilles, aleurodes). 25  
    * Plantes : Toutes plantes infestées par des insectes piqueurs-suceurs.  
    * Prévention : Lutter contre les insectes piqueurs-suceurs.  
    * Traitements : Nettoyer les feuilles avec de l'eau savonneuse (savon noir).25 Traiter la cause (insectes).  
* Maladies Bactériennes :

  * Chancre Bactérien :  
    * Symptômes : Lésions chancreuses sur l'écorce, écoulement de gomme, dépérissement des rameaux. 29  
    * Plantes : Arbres fruitiers (prunier, cerisier, pommier), marronnier. 29  
    * Prévention : Éviter blessures, désinfecter outils de taille, bouillie bordelaise. 40  
  * Feu Bactérien (*Erwinia amylovora*) :  
    * Symptômes : Flétrissement et noircissement rapide des fleurs, feuilles et jeunes pousses (aspect brûlé). Écoulements bactériens. 29  
    * Plantes : Poirier, pommier, cognassier, aubépine.  
    * Prévention : Variétés résistantes, éliminer sources d'inoculum, désinfecter outils.  
    * Traitements : Difficile, souvent arrachage des parties très atteintes ou de l'arbre. Cuivre en prévention.  
* Ravageurs :

  * Pucerons :  
    * Symptômes : Colonies d'insectes sur jeunes pousses, feuilles (souvent au revers), boutons floraux. Feuilles s'enroulent, se crispent. Présence de miellat et potentiellement de fumagine. 31  
    * Plantes : Rosiers, arbres fruitiers, légumes (tomates), la plupart des plantes. 31  
    * Traitements : Jet d'eau, savon noir 31, purin d'ortie (répulsif/traitement) 12, macération d'ail 45, huile de colza.44 Lutte biologique : larves de coccinelles, chrysopes.31  
  * Acariens (Araignées rouges/jaunes, Tétranyques) :  
    * Symptômes : Points jaunes/bruns sur les feuilles, aspect plombé/grisâtre. Fines toiles d'araignée visibles (surtout tétranyques). Feuilles se dessèchent et tombent. 25  
    * Plantes : Nombreuses plantes d'intérieur et d'extérieur (rosiers, tomates, haricots, fruitiers). 28  
    * Conditions : Temps chaud et sec. 25  
    * Traitements : Augmenter l'humidité (vaporisations d'eau), savon noir 44, huile de colza 44, décoction d'ail, purin d'ortie 31, soufre (acaricide).17 Lutte biologique : acariens prédateurs (*Phytoseiulus persimilis*).  
  * Cochenilles (farineuses, à bouclier) :  
    * Symptômes : Amas cotonneux blancs (farineuses) ou petits boucliers bruns/noirs sur tiges et feuilles. Miellat et fumagine. Affaiblissement de la plante. 31  
    * Plantes : Hortensias, lauriers, agrumes, plantes d'intérieur, oliviers.  
    * Traitements : Savon noir \+ alcool à brûler \+ huile végétale (application au pinceau ou pulvérisation) 48, huile de colza.44 Lutte biologique : coccinelle *Cryptolaemus montrouzieri*.  
  * Aleurodes (Mouches blanches) :  
    * Symptômes : Petits insectes blancs volants s'envolant lorsqu'on dérange la plante. Larves au revers des feuilles. Miellat et fumagine. 25  
    * Plantes : Tomates, choux, fuchsias, plantes de serre. 25  
    * Traitements : Savon noir 44, pièges jaunes englués.38 Lutte biologique : micro-guêpe *Encarsia formosa*.  
  * Limaces et Escargots :  
    * Symptômes : Traces brillantes de mucus, feuilles et jeunes plants grignotés.  
    * Plantes : Salades, hostas, jeunes plants en général.  
    * Traitements : Pièges à bière 44, barrières de cendres, coquilles d'œufs broyées ou coquilles d'huîtres 16, granulés à base de phosphate ferrique.51 Purin de fougère (répulsif).52 Nématodes anti-limaces.  
  * Chenilles défoliatrices (ex: Piéride du chou, Tordeuses, Pyrale du buis) :  
    * Symptômes : Feuilles mangées, parfois ne laissant que les nervures. Tiges et apex détruits (tordeuses). 32  
    * Plantes : Choux, buis, arbres fruitiers, rosiers, nombreuses plantes. 32  
    * Traitements : Ramassage manuel, *Bacillus thuringiensis* (Bt) 31, filets anti-insectes.  
  * Drosophile à ailes tachetées (*Drosophila suzukii*) :  
    * Symptômes : Petits trous de ponte sur fruits mûrs, larves se développant à l'intérieur, liquéfaction du fruit. 35  
    * Plantes : Cerises, fraises, framboises, mûres, myrtilles. 35  
    * Prévention/Lutte : Piégeage massif, récolte fréquente, élimination des fruits tombés.  
  * Punaise diabolique (*Halyomorpha halys*) :  
    * Symptômes : Piqûres sur fruits causant nécroses, malformations, zones spongieuses. 35  
    * Plantes : Très polyphage (pommiers, poiriers, noyers, vigne, petits fruits, maïs). 35  
    * Prévention/Lutte : Piégeage, destruction des adultes hivernants.  
  * Courtilière (Taupe-grillon) :  
    * Symptômes : Galeries souterraines, racines de jeunes plants sectionnées, plantules flétries. 53  
    * Plantes : Potager (apprécie l'humidité).  
    * Traitements : Marc de café ou purin d'ortie (répulsif olfactif), huile végétale dans le terrier. Travail du sol pour détruire nids et galeries. 53

Problématiques spécifiques à la région Auvergne-Rhône-Alpes :

* Sharka (Plum Pox Virus) : Maladie virale grave des arbres fruitiers à noyau (pêcher, abricotier, prunier). Fruits inconsommables. Dissémination par pucerons et matériel végétal contaminé. Lutte préventive : matériel sain, surveillance, arrachage. 34  
* Maladies et ravageurs des fruitiers (BSV AuRA) : En avril 2023, signalement de tavelure sur pommier, oïdium, pucerons, chenilles défoliatrices, moniliose sur pêcher/abricotier, cloque du pêcher, etc..37 Les bulletins de santé du végétal (BSV) de FREDON Auvergne-Rhône-Alpes sont une source clé pour le suivi régional.36  
* Gentiane : Sujette à la rouille et aux limaces. Purin de fougère comme solution écologique contre les limaces.52 La culture se fait principalement dans le Massif Central.54  
* Contamination des sols/eaux (PFAS) : Au sud de Lyon, une contamination par des PFAS a été détectée, entraînant des recommandations de ne pas consommer les fruits et légumes des jardins potagers de certains secteurs ni d'utiliser l'eau de puits privés.55 Bien que ce ne soit pas une "maladie de plante" classique, cela impacte directement les pratiques de jardinage et la sécurité alimentaire dans les zones concernées. L'application pourrait intégrer des alertes ou des conseils spécifiques si elle cible ces zones géographiques.  
* Moustique tigre : Fortement implanté en Auvergne-Rhône-Alpes, il est une nuisance et peut transmettre des maladies. La lutte implique l'élimination des eaux stagnantes.56 L'application pourrait inclure des conseils de prévention.

#### 4.3.3 Traitements Naturels et Biologiques (Synthèse)

De nombreux traitements naturels peuvent être employés :

* Savon Noir : Insecticide de contact (pucerons, cochenilles, acariens, aleurodes). Diluer 3-4 c.à.s. dans 1L d'eau chaude pour pucerons 48 ; 1 c.à.c. dans 1L pour cochenilles 48 ; 5ml de savon de Marseille liquide \+ 5ml bicarbonate dans 1L d'eau.49 Rincer le lendemain.  
* Huile de Colza : Contre acariens, cochenilles, pucerons, œufs. 1 c.à.s. d'huile \+ 1L d'eau \+ quelques gouttes de liquide vaisselle.44  
* Lait de Vache : Fongicide contre l'oïdium. Diluer 1 volume de lait pour 9 volumes d'eau.44  
* Bière : Piège à limaces et escargots.44  
* Bicarbonate de Soude : Fongicide (oïdium, mildiou, rouille). 1 c.à.c. dans 1L d'eau, parfois avec du savon noir.30  
* Purins et Décoctions de Plantes :  
  * Ortie : Fertilisant, fortifiant, répulsif pucerons, préventif maladies. Dilution 5-10%.1  
  * Prêle : Fongicide (mildiou, oïdium, rouille, cloque). Riche en silice. Dilution 10%.30  
  * Consoude : Riche en potasse, favorise floraison/fructification, active compost. Infusion : 500g fraîche pour 10L d'eau.41  
  * Ail : Insecticide (pucerons), fongicide. Décoction : 6 gousses/1L d'eau, bouillir 15 min.30  
  * Tanaisie, Camomille : Décoctions insectifuges/fongicides.41  
* Bouillie Bordelaise (Sulfate de cuivre \+ chaux) : Fongicide et bactéricide préventif (mildiou, tavelure, cloque, chancre).30 Utiliser avec modération (accumulation de cuivre dans le sol).57 Doses variables (4-25g/L).40  
* Soufre : Fongicide (oïdium), acaricide. Fleur de soufre (poudrage) ou soufre mouillable (pulvérisation).17

#### 4.3.4 Lutte Biologique

Utilisation d'organismes vivants pour contrôler les ravageurs 50 :

* Macro-organismes :  
  * Coccinelles (larves et adultes) : Contre les pucerons.31  
  * Chrysopes (larves) : Contre pucerons, thrips, petites chenilles.37  
  * Nématodes : Vers microscopiques contre limaces, larves de hannetons, otiorhynques, courtilières, carpocapses.37  
  * Guêpes parasitoïdes (ex: Trichogrammes, Encarsia) : Contre œufs de lépidoptères, aleurodes.51  
* Micro-organismes :  
  * *Bacillus thuringiensis* (Bt) : Bactérie produisant des toxines contre les chenilles de lépidoptères.31  
  * Champignons entomopathogènes (ex: *Beauveria bassiana*) : Infectent et tuent divers insectes.  
* Phéromones : Piégeage de détection, de masse, ou confusion sexuelle contre certains papillons (carpocapse, tordeuse).37

### **4.4 Carences Nutritionnelles**

Les carences en éléments nutritifs se manifestent par des symptômes spécifiques, souvent sur les feuilles.

| Élément | Symptômes de Carence | Causes Fréquentes | Solutions / Apports Correctifs (voir section Engrais) | Références |
| :---- | :---- | :---- | :---- | :---- |
| Azote (N) | Jaunissement feuilles (surtout anciennes, nervures incluses), croissance limitée, chute feuilles. | Sol pauvre/sableux, pot exigu. | Sang séché, purin d'ortie, corne broyée, urée, engrais NPK. | 6 |
| Phosphore (P) | Feuilles vert sombre virant au violacé (surtout au revers, nervures), plante chétive, rigide, fruits rares/petits/acides. | Sol lourd/argileux/tourbeux, pH inadapté (trop acide/alcalin), froid, excès d'eau. | Poudre d'os, farine de poisson, guano, engrais NPK. | 6 |
| Potassium (K) | Taches chlorotiques puis nécrotiques en périphérie du limbe (feuilles âgées), limbe incurvé vers le bas, mauvaise floraison/fructification (fruits mous, creux, mal colorés). | Sol léger/sableux, lessivage, excès Ca/Mg. | Sulfate de potassium, cendre de bois (avec modération), patenkali, consoude, engrais NPK. | 6 |
| Calcium (Ca) | Périphérie jeunes folioles vert pâle, lésions nécrotiques. Bourgeons terminaux brunissent/nécrosent. Extrémités pousses tordues, apex recourbé. Fruits avec altérations humides (ex: nécrose apicale tomate). | Sol acide, arrosages irréguliers (pots), excès Mg/K/Na. | Coquilles d'huîtres broyées, chaux (attention pH), lithothamne. | 6 |
| Magnésium (Mg) | Chlorose entre nervures (qui restent vertes) et/ou en périphérie du limbe (feuilles âgées d'abord). Tissus jaunis se nécrosent, feuilles tombent. | Sol calcaire, excès K, stress hydrique. | Sulfate de magnésium (Sel d'Epsom), patenkali, kiésérite, basalte broyé. | 6 |
| Fer (Fe) | Chlorose internervaire des jeunes feuilles (nervures vertes), feuilles peuvent devenir blanches. | Sol calcaire (pH élevé rend Fe non disponible), excès de phosphore. | Sulfate de fer, chélate de fer (plus assimilable en sol calcaire). | 2 |
| Manganèse (Mn) | Chlorose internervaire (feuilles jeunes/moyennes), marbrures, taches nécrotiques. Différent de carence Fe (touche feuilles plus âgées ou aspect différent). | Sol calcaire (pH élevé), excès de fer, sol sableux/tourbeux, sécheresse. | Sulfate de manganèse, mélanges d'oligo-éléments. | 2 |
| Bore (B) | Déformation jeunes feuilles, raccourcissement entre-nœuds, mauvaise nouaison, cœur brun (légumes racines), crevasses, nécroses, mort points de croissance. | Sol sableux/lessivé, pH élevé, sécheresse. | Borax (avec extrême précaution, toxique en excès), mélanges d'oligo-éléments. | 2 |
| Cuivre (Cu) | Décoloration et dessèchement extrémité jeunes feuilles (séparation net entre blanc/vert), aspect flétri, jeunes pousses flétrissent, fertilité fleurs réduite. | Sol granitique, sableux, riche en matière organique, pH élevé. | Sulfate de cuivre (bouillie bordelaise, avec modération), mélanges d'oligo-éléments. | 2 |
| Zinc (Zn) | Nanisme, réduction taille feuilles (rosette), jaunissement feuilles, floraison/fructification tardives, entre-nœuds courts. | Sol calcaire (pH élevé), excès de phosphore, manque matière organique. | Sulfate de zinc, mélanges d'oligo-éléments. | 2 |
| Molybdène (Mo) | Symptômes similaires à carence en azote (jaunissement généralisé, croissance ralentie), flétrissement feuilles, diminution fixation azote. | Sol acide. | Molybdate de sodium (très petites quantités), mélanges d'oligo-éléments, chaulage pour augmenter pH. | 2 |

Il est crucial de bien identifier la carence avant d'appliquer un correctif, car un excès d'un élément peut induire la carence d'un autre ou devenir toxique.3 L'analyse de sol et/ou de tissus végétaux est parfois nécessaire pour un diagnostic précis.3

## **Section 5 : Déploiement et Bonnes Pratiques Finales**

Une fois le développement de l'application "Violet Rikita" achevé et la base de connaissances intégrée, le processus de déploiement sera une étape clé.

### **5.1 Processus de Déploiement**

Le workflow de déploiement s'appuiera sur GitHub pour la gestion de version et Netlify pour l'hébergement.1 Les étapes générales sont :

1. Tests Finaux :

   * Exécuter l'ensemble des tests (unitaires, intégration, end-to-end) pour s'assurer de la stabilité de la version à déployer.  
   * Valider manuellement les fonctionnalités clés sur différents navigateurs et appareils.  
2. Préparation pour la Production :

   * S'assurer que toutes les configurations (clés API, endpoints, etc.) sont correctement paramétrées pour l'environnement de production (utilisation de variables d'environnement).  
   * Minimiser et optimiser les assets (CSS, JavaScript, images) via l'outil de build (Vite.js).1  
3. Gestion de Version (GitHub) :

   * Créer une branche de release ou taguer la version sur la branche principale (ex: main ou master).  
   * S'assurer que tous les changements sont commités avec des messages clairs et descriptifs.  
     * git add.  
     * git commit \-m "Release v1.0.0: Finalisation et préparation pour déploiement"  
   * Pousser les changements sur le dépôt distant GitHub : git push origin main (ou nom de la branche).  
4. Déploiement sur Netlify (ou Firebase Hosting) :

   * Netlify : Si Netlify est utilisé, il est généralement configuré pour se lier au dépôt GitHub. Un push sur la branche de production désignée (ex: main) déclenchera automatiquement un nouveau build et déploiement.  
     * Vérifier les logs de build sur Netlify pour s'assurer du succès de l'opération.  
   * Firebase Hosting :  
     * Se connecter à Firebase : firebase login (si ce n'est déjà fait).  
     * S'assurer que le projet Firebase est correctement initialisé pour l'hébergement : firebase init hosting (si non fait).  
     * Déployer l'application : firebase deploy \--only hosting.  
     * Si des Cloud Functions sont mises à jour, déployer également les fonctions : firebase deploy \--only functions. Ou un déploiement complet : firebase deploy. 1  
5. Vérification Post-Déploiement :

   * Tester l'application en production pour confirmer que tout fonctionne comme attendu.  
   * Surveiller les logs et les outils d'analyse pour détecter d'éventuels problèmes.

### **5.2 Importance des Bonnes Pratiques Continues**

Même après le déploiement initial, le maintien des bonnes pratiques est essentiel :

* Revue de Code : Continuer à pratiquer la revue de code pour les nouvelles fonctionnalités ou corrections.  
* Tests Continus : Maintenir et enrichir la suite de tests à mesure que l'application évolue.  
* Documentation : Tenir à jour la documentation (code, architecture, base de connaissances).  
* Gestion des Dépendances : Surveiller et mettre à jour régulièrement les dépendances du projet pour des raisons de sécurité et de performance.  
* Sauvegardes : S'assurer que les données Firestore sont régulièrement sauvegardées.

## **Conclusion**

Cette base de connaissances a été élaborée pour fournir à l'assistant de codage, Auggies, les informations techniques et botaniques nécessaires au développement et à l'enrichissement de l'application "Violet Rikita". L'accent a été mis sur la précision des données relatives aux engrais, aux maladies des plantes, aux carences nutritionnelles et à leurs remèdes, ainsi que sur l'importance d'une architecture logicielle et de pratiques de développement rigoureuses.

Il est impératif qu'Auggies adhère scrupuleusement aux directives architecturales, aux consignes de gestion des fichiers, et aux protocoles de sécurité, notamment concernant la clé API Gemini. La qualité, la maintenabilité et la robustesse de "Violet Rikita" dépendent directement du respect de ces principes.

En s'appuyant sur ce document, l'application "Violet Rikita" a le potentiel de devenir un outil précieux et fiable pour les amateurs de plantes, les guidant vers des pratiques de jardinage éclairées et respectueuses de l'environnement végétal. La collaboration continue et le souci du détail seront les garants du succès de ce projet.

