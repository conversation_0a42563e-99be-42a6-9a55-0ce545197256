### À l'attention de l'assistant de codage (Auggies, Gemini Code Assist, etc.)

Objet : Charte Graphique et Direction Artistique pour l'application "Violet Rikita"

Ce document détaille l'identité visuelle à respecter pour le développement de l'interface utilisateur. L'objectif est de créer une expérience moderne, élégante et intuitive, en s'inspirant fortement des maquettes fournies.

### 1\. Ambiance Générale & Philosophie

* Thème : Dark Mode (Mode Sombre) professionnel et technologique.  
* Mots-clés : Élégant, premium, épuré, futuriste, spacieux, confiant.  
* Impression : L'utilisateur doit sentir qu'il utilise un outil haut de gamme, à la fois puissant et simple d'accès. L'interface doit être reposante pour les yeux tout en mettant en valeur les informations importantes grâce à des touches de couleur vives.

### 2\. Palette de Couleurs

La palette est construite autour d'un fond très sombre avec des accents violets et lavande pour créer un contraste saisissant.

* Arrière-plan principal : Un violet très sombre, presque noir, avec une légère saturation.  
  * CSS : background-color: \#100f1c; (ou un dégradé subtil entre \#1A1A2E et \#100f1c)  
* Couleur des Conteneurs/Cartes : Un violet légèrement plus clair que le fond pour créer une hiérarchie visuelle. Les cartes semblent flotter au-dessus de l'arrière-plan.  
  * CSS : background-color: \#1c1a31;  
* Accent Primaire (Boutons, Titres, Icônes actives) : Un dégradé lumineux allant du rose au lavande.  
  * CSS (gradient) : background: linear-gradient(90deg, \#d385f5, \#a364f7);  
* Texte Principal (Titres) : Blanc pur pour une lisibilité maximale.  
  * CSS : color: \#FFFFFF;  
* Texte Secondaire (Paragraphes, descriptions) : Un gris très clair pour adoucir le contraste.  
  * CSS : color: \#E0E0E0; ou color: \#BDBDBD;  
* Bordures & Lignes : Une couleur violette/grise très subtile pour délimiter les sections sans être agressif.  
  * CSS : border-color: \#2c2a4a;

### 3\. Typographie

La police de caractères doit être une sans-serif moderne, propre et très lisible.

* Police recommandée : Inter ou Poppins (disponibles sur Google Fonts).  
* Titres de section (ex: "Why Choose Us?") : Police en gras (Bold, 700), avec un espacement des lettres légèrement augmenté pour un look aéré.  
* Texte de corps : Police en poids normal (Regular, 400).  
* Hiérarchie : Jouer avec les tailles de police (ex: text-4xl pour les grands titres, text-xl pour les titres de carte, text-base pour le corps) et les poids pour bien structurer l'information.

### 4\. Composants et Styles

* Cartes (Cards) :  
  * Coins très arrondis (rounded-xl ou rounded-2xl en Tailwind CSS).  
  * Un padding généreux à l'intérieur (p-6 ou p-8).  
  * Peuvent avoir une bordure très subtile (border border-\[\#2c2a4a\]) ou une ombre portée douce pour les détacher du fond.  
  * Structure interne : souvent une icône ou une image en haut, suivie d'un titre en gras, puis d'un texte descriptif.  
* Boutons (Buttons) :  
  * Bouton Primaire (Call to Action) : Fond avec le dégradé lavande/rose, texte blanc, coins arrondis (rounded-lg), pas de bordure. Effet au survol (hover): légère augmentation de la luminosité ou de la taille (scale-105).  
  * Bouton Secondaire : Fond transparent, bordure de couleur lavande (border border-\[\#a364f7\]), texte couleur lavande. Au survol, le fond peut se remplir de la couleur lavande.  
* Champs de formulaire (Inputs) :  
  * Fond de la même couleur que les cartes (\#1c1a31).  
  * Bordure subtile (\#2c2a4a).  
  * Quand le champ est sélectionné (focus), la bordure prend la couleur d'accent lavande.  
  * Texte à l'intérieur en gris clair.  
  * Coins arrondis (rounded-lg).  
* Disposition (Layout) :  
  * Utilisation intensive de la grille (Grid) et de Flexbox pour aligner les éléments.  
  * Sections bien définies avec des titres clairs.  
  * Beaucoup d'espace négatif (marges et paddings) pour laisser l'interface respirer et ne pas surcharger l'utilisateur.

En respectant scrupuleusement ces directives, l'application "Violet Rikita" aura une identité visuelle forte, professionnelle et cohérente.

