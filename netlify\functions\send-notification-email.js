/**
 * Fonction Netlify pour l'envoi d'emails de notification FloraSynth
 * Utilise un service d'email externe (ex: SendGrid, Mailgun, ou SMTP)
 */

const nodemailer = require('nodemailer');

// Configuration du transporteur email
const createTransporter = () => {
  // Option 1: Gmail SMTP (pour développement)
  if (process.env.GMAIL_USER && process.env.GMAIL_APP_PASSWORD) {
    return nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_APP_PASSWORD
      }
    });
  }

  // Option 2: SendGrid
  if (process.env.SENDGRID_API_KEY) {
    return nodemailer.createTransporter({
      host: 'smtp.sendgrid.net',
      port: 587,
      secure: false,
      auth: {
        user: 'apikey',
        pass: process.env.SENDGRID_API_KEY
      }
    });
  }

  // Option 3: SMTP générique
  if (process.env.SMTP_HOST) {
    return nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD
      }
    });
  }

  throw new Error('Aucune configuration email trouvée');
};

exports.handler = async (event, context) => {
  // Vérifier la méthode HTTP
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: JSON.stringify({ error: 'Méthode non autorisée' })
    };
  }

  // Gérer les requêtes OPTIONS (CORS)
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: ''
    };
  }

  try {
    // Parser les données de la requête
    const { to, subject, html, priority } = JSON.parse(event.body);

    // Validation des données
    if (!to || !subject || !html) {
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          error: 'Données manquantes: to, subject et html sont requis' 
        })
      };
    }

    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to)) {
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ error: 'Adresse email invalide' })
      };
    }

    // Créer le transporteur
    const transporter = createTransporter();

    // Configuration de l'email
    const mailOptions = {
      from: {
        name: 'FloraSynth',
        address: process.env.FROM_EMAIL || process.env.GMAIL_USER || '<EMAIL>'
      },
      to: to,
      subject: subject,
      html: html,
      // Définir la priorité si spécifiée
      priority: priority === 'urgent' ? 'high' : 'normal'
    };

    // Envoyer l'email
    console.log(`📧 Envoi d'email à ${to} avec le sujet: ${subject}`);
    const info = await transporter.sendMail(mailOptions);
    
    console.log(`✅ Email envoyé avec succès. Message ID: ${info.messageId}`);

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        success: true, 
        messageId: info.messageId,
        message: 'Email envoyé avec succès'
      })
    };

  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi de l\'email:', error);

    // Gestion des erreurs spécifiques
    let errorMessage = 'Erreur lors de l\'envoi de l\'email';
    let statusCode = 500;

    if (error.code === 'EAUTH') {
      errorMessage = 'Erreur d\'authentification email';
      statusCode = 401;
    } else if (error.code === 'ECONNECTION') {
      errorMessage = 'Erreur de connexion au serveur email';
      statusCode = 503;
    } else if (error.responseCode === 550) {
      errorMessage = 'Adresse email de destination invalide';
      statusCode = 400;
    }

    return {
      statusCode: statusCode,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    };
  }
};
