import React, { useState } from 'react';
import { Calendar, Clock, CheckCircle, Alert<PERSON>riangle, Bell, History } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs';
import { useDiagnosticEvents, useActionHistory } from '../../../hooks/useNotifications';
import { DiagnosticEvent, UserActionHistory } from '../../../types/notifications';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

interface PlantNotificationHistoryProps {
  plantId: string;
  plantName: string;
}

/**
 * Composant pour afficher l'historique complet d'une plante (diagnostics + notifications)
 */
export const PlantNotificationHistory: React.FC<PlantNotificationHistoryProps> = ({ 
  plantId, 
  plantName 
}) => {
  const { events, completeEvent } = useDiagnosticEvents({ plantId });
  const { history } = useActionHistory(plantId);
  const [activeTab, setActiveTab] = useState('events');

  const pendingEvents = events.filter(event => !event.completed);
  const completedEvents = events.filter(event => event.completed);

  const handleCompleteEvent = async (eventId: string) => {
    try {
      await completeEvent(eventId);
    } catch (error) {
      console.error('Erreur lors de la finalisation de l\'événement:', error);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Journal de {plantName}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="events" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Événements
              {pendingEvents.length > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {pendingEvents.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="completed" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Terminés
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Historique
            </TabsTrigger>
          </TabsList>

          {/* Événements en cours */}
          <TabsContent value="events" className="space-y-4">
            {pendingEvents.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Aucun événement en cours pour cette plante</p>
              </div>
            ) : (
              <div className="space-y-3">
                {pendingEvents.map((event) => (
                  <EventCard
                    key={event.id}
                    event={event}
                    onComplete={handleCompleteEvent}
                    showCompleteButton
                  />
                ))}
              </div>
            )}
          </TabsContent>

          {/* Événements terminés */}
          <TabsContent value="completed" className="space-y-4">
            {completedEvents.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Aucun événement terminé pour cette plante</p>
              </div>
            ) : (
              <div className="space-y-3">
                {completedEvents.map((event) => (
                  <EventCard key={event.id} event={event} />
                ))}
              </div>
            )}
          </TabsContent>

          {/* Historique des actions */}
          <TabsContent value="history" className="space-y-4">
            {history.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Aucun historique d'actions pour cette plante</p>
              </div>
            ) : (
              <div className="space-y-3">
                {history.map((action) => (
                  <ActionCard key={action.id} action={action} />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

/**
 * Composant pour afficher un événement individuel
 */
interface EventCardProps {
  event: DiagnosticEvent;
  onComplete?: (eventId: string) => void;
  showCompleteButton?: boolean;
}

const EventCard: React.FC<EventCardProps> = ({ event, onComplete, showCompleteButton = false }) => {
  const isOverdue = !event.completed && event.nextActionDate.toDate() < new Date();
  
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-red-500 bg-red-50';
      case 'high': return 'border-orange-500 bg-orange-50';
      case 'medium': return 'border-yellow-500 bg-yellow-50';
      case 'low': return 'border-green-500 bg-green-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  const getStatusIcon = () => {
    if (event.completed) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (isOverdue) return <AlertTriangle className="h-4 w-4 text-red-600" />;
    return <Clock className="h-4 w-4 text-blue-600" />;
  };

  return (
    <div className={`p-4 border-l-4 rounded-r-lg ${getPriorityColor(event.priority)}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            {getStatusIcon()}
            <h4 className="font-medium text-gray-900">{event.title}</h4>
            <Badge variant="outline" className="text-xs">
              {event.eventType}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {event.priority}
            </Badge>
          </div>
          
          <p className="text-gray-700 text-sm mb-2">{event.description}</p>
          
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2 text-gray-600">
              <Calendar className="h-3 w-3" />
              <span>Prochaine action: {event.nextActionType}</span>
            </div>
            <div className="flex items-center gap-2 text-gray-600">
              <Clock className="h-3 w-3" />
              <span>
                Prévu le {event.nextActionDate.toDate().toLocaleDateString('fr-FR')}
                {isOverdue && <span className="text-red-600 ml-1">(En retard)</span>}
              </span>
            </div>
          </div>

          {event.geminiRecommendation && (
            <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm">
              <div className="font-medium text-blue-800 mb-1">Recommandation Gemini:</div>
              <p className="text-blue-700">{event.geminiRecommendation}</p>
            </div>
          )}

          <p className="text-xs text-gray-500 mt-2">
            Créé {formatDistanceToNow(event.createdAt.toDate(), { addSuffix: true, locale: fr })}
          </p>
        </div>

        {showCompleteButton && !event.completed && onComplete && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onComplete(event.id)}
            className="ml-4"
          >
            <CheckCircle className="h-4 w-4 mr-1" />
            Terminer
          </Button>
        )}
      </div>
    </div>
  );
};

/**
 * Composant pour afficher une action de l'historique
 */
interface ActionCardProps {
  action: UserActionHistory;
}

const ActionCard: React.FC<ActionCardProps> = ({ action }) => {
  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'diagnostic': return <Bell className="h-4 w-4 text-blue-600" />;
      case 'treatment_applied': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'event_completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'event_skipped': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default: return <History className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionLabel = (actionType: string) => {
    switch (actionType) {
      case 'diagnostic': return 'Diagnostic';
      case 'treatment_applied': return 'Traitement appliqué';
      case 'event_completed': return 'Événement terminé';
      case 'event_skipped': return 'Événement ignoré';
      default: return 'Action';
    }
  };

  return (
    <div className="p-3 border border-gray-200 rounded-lg bg-white">
      <div className="flex items-start gap-3">
        {getActionIcon(action.actionType)}
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-gray-900">{getActionLabel(action.actionType)}</span>
            <span className="text-xs text-gray-500">
              {action.actionDate.toDate().toLocaleDateString('fr-FR')}
            </span>
          </div>
          <p className="text-gray-700 text-sm">{action.description}</p>
          {action.metadata && (
            <div className="mt-2 text-xs text-gray-500">
              {action.metadata.disease && (
                <span>Maladie détectée: {action.metadata.disease}</span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
