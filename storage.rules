rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Fonction utilitaire pour vérifier l'authentification
    function isAuthenticated() {
      return request.auth != null;
    }

    // Fonction pour vérifier la propriété d'un fichier
    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    // Fonction pour valider les images
    function isValidImage(resource) {
      return resource.size < 5 * 1024 * 1024 && // 5MB max
             resource.contentType.matches('image/.*');
    }

    // Images de plantes utilisateur
    match /users/{userId}/plants/{plantId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isOwner(userId) && isValidImage(resource);
      allow delete: if isOwner(userId);
    }

    // Images de profil utilisateur
    match /users/{userId}/profile/{fileName} {
      allow read: if true; // Images de profil publiques
      allow write: if isOwner(userId) && isValidImage(resource);
      allow delete: if isOwner(userId);
    }

    // Documents privés utilisateur
    match /users/{userId}/documents/{fileName} {
      allow read, write: if isOwner(userId);
      allow delete: if isOwner(userId);
    }

    // Fichiers publics (lecture seule pour tous)
    match /public/{fileName} {
      allow read: if true;
      allow write: if false; // Géré par les Cloud Functions
    }

    // Règles par défaut (tout refuser)
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
