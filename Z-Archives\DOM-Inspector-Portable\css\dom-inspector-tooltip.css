/**
 * 🔍 DOM Inspector Tooltip - FlexoDiv Site
 * Système de tooltip visuel pour l'inspection des éléments DOM
 * Compatible avec la stack technique autorisée (HTML/CSS, Alpine.js, GSAP)
 * 
 * @version 1.0.0
 * <AUTHOR>
 * @date 2025-06-22
 */

/* Variables CSS pour la cohérence */
:root {
  --tooltip-bg: rgba(30, 41, 59, 0.95);
  --tooltip-border: #3b82f6;
  --tooltip-text: #f8fafc;
  --tooltip-accent: #10b981;
  --tooltip-warning: #f59e0b;
  --tooltip-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  --tooltip-radius: 8px;
  --tooltip-z-index: 10000;
}

/* Mode inspection activé */
.dom-inspector-active {
  cursor: crosshair !important;
}

.dom-inspector-active * {
  cursor: crosshair !important;
}

/* Surbrillance des éléments survolés */
.dom-inspector-highlight {
  outline: 2px solid var(--tooltip-border) !important;
  outline-offset: 2px !important;
  background-color: rgba(59, 130, 246, 0.1) !important;
  transition: all 0.2s ease !important;
}

/* Container principal du tooltip */
.dom-inspector-tooltip {
  position: fixed;
  background: var(--tooltip-bg);
  border: 1px solid var(--tooltip-border);
  border-radius: var(--tooltip-radius);
  padding: 12px 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: var(--tooltip-text);
  box-shadow: var(--tooltip-shadow);
  z-index: var(--tooltip-z-index);
  max-width: 400px;
  min-width: 250px;
  max-height: 80vh; /* Nouvelle : hauteur maximale */
  pointer-events: none;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  overflow: hidden; /* Nouvelle : masquer le débordement */
}

/* Tooltip verrouillé */
.dom-inspector-tooltip.locked {
  pointer-events: auto;
  border-color: #f59e0b;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3), var(--tooltip-shadow);
}

/* Wrapper de contenu avec scroll invisible */
.tooltip-content-wrapper {
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* Scrollbar invisible pour WebKit */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE et Edge */
}

.tooltip-content-wrapper::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.dom-inspector-tooltip.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Indicateur de verrouillage */
.tooltip-lock-indicator {
  color: #f59e0b;
  font-weight: bold;
  margin-left: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Aide contextuelle */
.tooltip-help {
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 4px;
  border-left: 3px solid #f59e0b;
  font-size: 10px;
  color: #f59e0b;
}

/* En-tête du tooltip */
.tooltip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
}

.tooltip-title {
  font-weight: bold;
  color: var(--tooltip-accent);
  font-size: 13px;
}

.tooltip-tag {
  background: var(--tooltip-border);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  text-transform: uppercase;
}

/* Sections d'information */
.tooltip-section {
  margin-bottom: 8px;
}

.tooltip-section:last-child {
  margin-bottom: 0;
}

.tooltip-label {
  color: var(--tooltip-accent);
  font-weight: bold;
  font-size: 11px;
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tooltip-value {
  color: var(--tooltip-text);
  word-break: break-all;
  margin-bottom: 4px;
}

.tooltip-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tooltip-list li {
  padding: 2px 0;
  color: var(--tooltip-text);
}

.tooltip-list li:before {
  content: "▸ ";
  color: var(--tooltip-accent);
  margin-right: 4px;
}

/* Boutons d'action */
.tooltip-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid rgba(59, 130, 246, 0.3);
}

.tooltip-btn {
  background: var(--tooltip-border);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  pointer-events: auto;
}

.tooltip-btn:hover {
  background: var(--tooltip-accent);
  transform: translateY(-1px);
}

.tooltip-btn.copy-btn {
  background: var(--tooltip-warning);
}

.tooltip-btn.copy-btn:hover {
  background: #d97706;
}

/* Indicateur de copie réussie */
.copy-success {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--tooltip-accent);
  color: white;
  padding: 8px 16px;
  border-radius: var(--tooltip-radius);
  font-size: 12px;
  z-index: calc(var(--tooltip-z-index) + 1);
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.3s ease;
}

.copy-success.show {
  opacity: 1;
  transform: translateX(0);
}

/* Style spécial pour l'export JSON */
.copy-success.json-export {
  background: linear-gradient(135deg, var(--tooltip-accent), #059669);
  border: 1px solid #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  font-weight: bold;
}

.copy-success.json-export small {
  font-size: 10px;
  opacity: 0.9;
  display: block;
  margin-top: 2px;
}

/* Style spécial pour les balises stratégiques */
.copy-success.strategic-tag {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border: 1px solid #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  font-weight: bold;
}

.copy-success.strategic-tag small {
  font-size: 10px;
  opacity: 0.9;
  display: block;
  margin-top: 2px;
}

/* Message d'erreur de copie */
.copy-error {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #dc2626;
  color: white;
  padding: 12px 20px;
  border-radius: var(--tooltip-radius);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  font-weight: 600;
  box-shadow: var(--tooltip-shadow);
  z-index: calc(var(--tooltip-z-index) + 10);
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.3s ease;
  pointer-events: none;
  text-align: center;
}

.copy-error.show {
  opacity: 1;
  transform: translateX(0);
}

.copy-error small {
  font-size: 11px;
  opacity: 0.8;
}

/* Modal pour afficher le texte sélectionnable */
.text-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: calc(var(--tooltip-z-index) + 20);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  animation: modalFadeIn 0.3s ease forwards;
}

.text-modal-overlay.closing {
  animation: modalFadeOut 0.3s ease forwards;
}

.text-modal {
  background: var(--tooltip-bg);
  border: 1px solid var(--tooltip-border);
  border-radius: var(--tooltip-radius);
  width: 90%;
  max-width: 600px;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  box-shadow: var(--tooltip-shadow);
  transform: scale(0.9);
  animation: modalScaleIn 0.3s ease forwards;
}

.text-modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--tooltip-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-modal-header h3 {
  margin: 0;
  color: var(--tooltip-text);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
}

.text-modal-close {
  background: none;
  border: none;
  color: var(--tooltip-text);
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.text-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.text-modal-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.text-modal-textarea {
  width: 100%;
  height: 300px;
  background: var(--tooltip-bg);
  border: 1px solid var(--tooltip-border);
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: var(--tooltip-text);
  resize: none;
  outline: none;
}

.text-modal-textarea:focus {
  border-color: var(--tooltip-accent);
}

.text-modal-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--tooltip-border);
  text-align: center;
}

.text-modal-footer small {
  color: var(--tooltip-text);
  opacity: 0.7;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
}

@keyframes modalFadeIn {
  to { opacity: 1; }
}

@keyframes modalFadeOut {
  to { opacity: 0; }
}

@keyframes modalScaleIn {
  to { transform: scale(1); }
}

/* Toggle button pour activer/désactiver l'inspecteur - Position discrète */
.dom-inspector-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--tooltip-bg);
  border: 1px solid var(--tooltip-border);
  color: var(--tooltip-text);
  padding: 8px 12px;
  border-radius: var(--tooltip-radius);
  font-size: 11px;
  cursor: pointer;
  z-index: var(--tooltip-z-index);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  opacity: 0.7;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dom-inspector-toggle:hover {
  background: var(--tooltip-border);
  transform: translateY(-2px);
  box-shadow: var(--tooltip-shadow);
  opacity: 1;
}

.dom-inspector-toggle.active {
  background: var(--tooltip-accent);
  border-color: var(--tooltip-accent);
  opacity: 1;
  animation: pulse 2s infinite;
}

/* Animation pour le bouton actif */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
  100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
}

/* Responsive */
@media (max-width: 768px) {
  .dom-inspector-tooltip {
    max-width: 300px;
    min-width: 200px;
    font-size: 11px;
  }

  .dom-inspector-toggle {
    bottom: 10px;
    right: 10px;
    padding: 6px 10px;
    font-size: 10px;
  }

  .copy-success {
    top: 10px;
    right: 10px;
    font-size: 11px;
  }

  .copy-error {
    top: 10px;
    right: 10px;
    font-size: 11px;
  }
}

/* Animation d'apparition */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dom-inspector-tooltip.animate-in {
  animation: tooltipFadeIn 0.3s ease forwards;
}

/* Styles pour les éléments spéciaux */
.tooltip-file-path {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: rgba(59, 130, 246, 0.2);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 11px;
}

.tooltip-dependency {
  color: var(--tooltip-warning);
  font-weight: bold;
}

.tooltip-element-info {
  background: rgba(16, 185, 129, 0.2);
  padding: 4px 6px;
  border-radius: 4px;
  margin: 2px 0;
}

/* Styles pour les suggestions de balises stratégiques */
.tooltip-strategic-suggestion {
  color: #10b981;
  font-weight: bold;
}

.tooltip-strategic-suggestion.priority-high {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.tooltip-strategic-suggestion.priority-medium {
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.tooltip-strategic-suggestion.priority-low {
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

/* Section de recommandation stratégique */
.tooltip-strategic-recommendation {
  border-left: 4px solid #10b981;
  background: rgba(16, 185, 129, 0.05);
  margin: 8px 0;
  padding: 8px;
  border-radius: 4px;
}

.tooltip-strategic-recommendation.priority-high {
  border-left-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.tooltip-strategic-recommendation.priority-medium {
  border-left-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}

.tooltip-strategic-recommendation.priority-low {
  border-left-color: #6b7280;
  background: rgba(107, 114, 128, 0.05);
}

/* Balise stratégique détectée */
.tooltip-strategic-tag {
  color: #10b981;
  font-weight: bold;
  background: rgba(16, 185, 129, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Aucune balise détectée */
.tooltip-no-tag {
  color: #f59e0b;
  font-style: italic;
}

/* NOUVEAUX STYLES pour les améliorations ultra-précises */
.tooltip-line-number {
  color: #ff6b6b;
  font-weight: bold;
  background: rgba(255, 107, 107, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.tooltip-search-type {
  color: var(--tooltip-accent);
  font-weight: bold;
  text-transform: uppercase;
  font-size: 10px;
  background: rgba(16, 185, 129, 0.2);
  padding: 1px 3px;
  border-radius: 2px;
  margin-right: 4px;
}

.tooltip-instruction {
  background: rgba(245, 158, 11, 0.1);
  border-left: 3px solid var(--tooltip-warning);
  padding: 4px 8px;
  margin: 4px 0;
  font-size: 11px;
  line-height: 1.3;
}

.tooltip-priority {
  color: #ef4444;
  font-weight: bold;
  font-size: 10px;
}

/* Animation pour les nouveaux éléments */
.tooltip-line-number {
  animation: highlightPulse 2s infinite;
}

@keyframes highlightPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* NOUVEAUX STYLES pour les balises stratégiques */
.tooltip-strategic-tag {
  color: #10b981;
  font-weight: bold;
  background: rgba(16, 185, 129, 0.2);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  animation: strategicPulse 2s infinite;
}

.tooltip-no-tag {
  color: #f59e0b;
  font-weight: bold;
  background: rgba(245, 158, 11, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.tooltip-strategic-suggestion {
  background: rgba(16, 185, 129, 0.1);
  border: 1px dashed var(--tooltip-accent);
  padding: 6px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 10px;
  color: var(--tooltip-accent);
  word-break: break-all;
  margin: 4px 0;
}

@keyframes strategicPulse {
  0%, 100% {
    background: rgba(16, 185, 129, 0.2);
    transform: scale(1);
  }
  50% {
    background: rgba(16, 185, 129, 0.4);
    transform: scale(1.02);
  }
}

/* ========================================
   NOUVEAUX STYLES POUR POSITIONNEMENT AUTOMATIQUE
   Détection intelligente de position du tooltip
   Version Portable - DOM Inspector FlexoDiv
   ======================================== */

/* Styles de base pour les différentes positions */
.dom-inspector-tooltip.position-bottom-right {
  transform-origin: top left;
}

.dom-inspector-tooltip.position-top-right {
  transform-origin: bottom left;
}

.dom-inspector-tooltip.position-bottom-left {
  transform-origin: top right;
}

.dom-inspector-tooltip.position-top-left {
  transform-origin: bottom right;
}

.dom-inspector-tooltip.position-right-center {
  transform-origin: center left;
}

.dom-inspector-tooltip.position-left-center {
  transform-origin: center right;
}

/* Indicateurs visuels pour les positions (optionnel, pour le debug) */
.dom-inspector-tooltip.position-bottom-right::before {
  content: "↗";
  position: absolute;
  top: -8px;
  left: -8px;
  font-size: 10px;
  color: var(--tooltip-accent);
  opacity: 0.5;
  display: none; /* Masqué par défaut, peut être activé pour le debug */
}

.dom-inspector-tooltip.position-top-right::before {
  content: "↘";
  position: absolute;
  bottom: -8px;
  left: -8px;
  font-size: 10px;
  color: var(--tooltip-accent);
  opacity: 0.5;
  display: none;
}

.dom-inspector-tooltip.position-bottom-left::before {
  content: "↖";
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 10px;
  color: var(--tooltip-accent);
  opacity: 0.5;
  display: none;
}

.dom-inspector-tooltip.position-top-left::before {
  content: "↙";
  position: absolute;
  bottom: -8px;
  right: -8px;
  font-size: 10px;
  color: var(--tooltip-accent);
  opacity: 0.5;
  display: none;
}

.dom-inspector-tooltip.position-right-center::before {
  content: "←";
  position: absolute;
  top: 50%;
  left: -8px;
  transform: translateY(-50%);
  font-size: 10px;
  color: var(--tooltip-accent);
  opacity: 0.5;
  display: none;
}

.dom-inspector-tooltip.position-left-center::before {
  content: "→";
  position: absolute;
  top: 50%;
  right: -8px;
  transform: translateY(-50%);
  font-size: 10px;
  color: var(--tooltip-accent);
  opacity: 0.5;
  display: none;
}

/* Animations d'entrée spécifiques selon la position */
.dom-inspector-tooltip.position-bottom-right.visible {
  animation: slideInBottomRight 0.3s ease;
}

.dom-inspector-tooltip.position-top-right.visible {
  animation: slideInTopRight 0.3s ease;
}

.dom-inspector-tooltip.position-bottom-left.visible {
  animation: slideInBottomLeft 0.3s ease;
}

.dom-inspector-tooltip.position-top-left.visible {
  animation: slideInTopLeft 0.3s ease;
}

.dom-inspector-tooltip.position-right-center.visible {
  animation: slideInRight 0.3s ease;
}

.dom-inspector-tooltip.position-left-center.visible {
  animation: slideInLeft 0.3s ease;
}

/* Animations keyframes pour chaque direction */
@keyframes slideInBottomRight {
  from {
    opacity: 0;
    transform: translate(-10px, -10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(0, 0) scale(1);
  }
}

@keyframes slideInTopRight {
  from {
    opacity: 0;
    transform: translate(-10px, 10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(0, 0) scale(1);
  }
}

@keyframes slideInBottomLeft {
  from {
    opacity: 0;
    transform: translate(10px, -10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(0, 0) scale(1);
  }
}

@keyframes slideInTopLeft {
  from {
    opacity: 0;
    transform: translate(10px, 10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(0, 0) scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-15px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(15px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Mode debug pour visualiser les positions (activable via console) */
.dom-inspector-debug .dom-inspector-tooltip::before {
  display: block !important;
}

/* Amélioration responsive pour le positionnement automatique */
@media (max-width: 768px) {
  .dom-inspector-tooltip {
    /* Sur mobile, privilégier les positions centrées */
    max-width: 280px;
    min-width: 180px;
  }

  /* Ajustements spécifiques pour mobile */
  .dom-inspector-tooltip.position-bottom-right,
  .dom-inspector-tooltip.position-bottom-left {
    /* Réduire l'offset sur mobile pour éviter de sortir de l'écran */
    transform: translateY(-5px);
  }

  .dom-inspector-tooltip.position-top-right,
  .dom-inspector-tooltip.position-top-left {
    transform: translateY(5px);
  }
}

/* Indicateur de qualité de position (pour le debug avancé) */
.dom-inspector-tooltip[data-position-score="100"] {
  border-color: var(--tooltip-accent); /* Position parfaite */
}

.dom-inspector-tooltip[data-position-score^="9"] {
  border-color: #10b981; /* Très bonne position */
}

.dom-inspector-tooltip[data-position-score^="8"] {
  border-color: #f59e0b; /* Position acceptable */
}

.dom-inspector-tooltip[data-position-score^="7"],
.dom-inspector-tooltip[data-position-score^="6"] {
  border-color: #ef4444; /* Position sous-optimale */
}
