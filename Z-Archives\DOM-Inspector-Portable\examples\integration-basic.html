<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intégration Basique - DOM Inspector</title>
    
    <!-- VOS STYLES EXISTANTS -->
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
    
    <!-- DOM INSPECTOR CSS - AJOUTEZ CETTE LIGNE -->
    <link rel="stylesheet" href="../css/dom-inspector-tooltip.css">
</head>

<!-- AJOUTEZ x-data="domInspector" SUR LE BODY -->
<body x-data="domInspector">
    
    <div class="container">
        <h1>🔍 Exemple d'Intégration Basique</h1>
        
        <div class="alert">
            <strong>✅ DOM Inspector Intégré !</strong><br>
            Cliquez sur le bouton "🔍 Inspecteur DOM" en bas à droite pour commencer l'inspection.
        </div>
        
        <div class="section">
            <h2>Section 1 - Navigation</h2>
            <nav>
                <button class="btn">Accueil</button>
                <button class="btn">À propos</button>
                <button class="btn">Services</button>
                <button class="btn">Contact</button>
            </nav>
        </div>
        
        <div class="section">
            <h2>Section 2 - Formulaire</h2>
            <form>
                <div class="form-group">
                    <label for="name">Nom :</label>
                    <input type="text" id="name" class="form-control" placeholder="Votre nom">
                </div>
                
                <div class="form-group">
                    <label for="email">Email :</label>
                    <input type="email" id="email" class="form-control" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="message">Message :</label>
                    <textarea id="message" class="form-control" rows="4" placeholder="Votre message"></textarea>
                </div>
                
                <button type="submit" class="btn">Envoyer</button>
            </form>
        </div>
        
        <div class="section">
            <h2>Section 3 - Contenu</h2>
            <p>Ce paragraphe contient du texte de démonstration pour tester l'inspection DOM.</p>
            
            <ul>
                <li>Premier élément de liste</li>
                <li>Deuxième élément de liste</li>
                <li>Troisième élément de liste</li>
            </ul>
            
            <div style="display: flex; gap: 10px; margin-top: 20px;">
                <div style="flex: 1; padding: 15px; background: #e3f2fd; border-radius: 4px;">
                    <h4>Colonne 1</h4>
                    <p>Contenu de la première colonne.</p>
                </div>
                <div style="flex: 1; padding: 15px; background: #f3e5f5; border-radius: 4px;">
                    <h4>Colonne 2</h4>
                    <p>Contenu de la deuxième colonne.</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Instructions d'Utilisation</h2>
            <ol>
                <li><strong>Activez l'inspecteur</strong> : Cliquez sur le bouton "🔍 Inspecteur DOM" en bas à droite</li>
                <li><strong>Inspectez les éléments</strong> : Survolez n'importe quel élément de cette page</li>
                <li><strong>Copiez les informations</strong> : Utilisez CTRL+C pour copier le rapport complet</li>
                <li><strong>Verrouillez le tooltip</strong> : Utilisez CTRL+Q pour fixer le tooltip et naviguer dedans</li>
                <li><strong>Désactivez</strong> : Appuyez sur ÉCHAP ou recliquez sur le bouton</li>
            </ol>
            
            <h3>Raccourcis Clavier</h3>
            <ul>
                <li><code>CTRL+C</code> - Copier le rapport complet</li>
                <li><code>CTRL+Q</code> - Verrouiller/déverrouiller le tooltip</li>
                <li><code>CTRL+SHIFT+C</code> - Export JSON avancé</li>
                <li><code>CTRL+ALT+S</code> - Générer une balise stratégique</li>
                <li><code>ÉCHAP</code> - Désactiver l'inspecteur</li>
            </ul>
        </div>
    </div>
    
    <!-- VOS SCRIPTS EXISTANTS -->
    <script>
        // Votre JavaScript existant
        console.log('Application chargée');
        
        // Exemple d'interaction
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log('Bouton cliqué:', this.textContent);
            });
        });
    </script>
    
    <!-- DOM INSPECTOR DEPENDENCIES - AJOUTEZ CES LIGNES -->
    <script src="../dependencies/alpinejs/alpine.min.js" defer></script>
    
    <!-- DOM INSPECTOR SCRIPT - AJOUTEZ CETTE LIGNE -->
    <script src="../js/dom-inspector-tooltip.js"></script>
    
</body>
</html>

<!--
═══════════════════════════════════════════════════════════════════
🔧 GUIDE D'INTÉGRATION RAPIDE
═══════════════════════════════════════════════════════════════════

Pour intégrer le DOM Inspector dans votre projet existant :

1. COPIEZ le dossier DOM-Inspector-Portable dans votre projet

2. AJOUTEZ ces 3 lignes dans votre HTML :

   Dans le <head> :
   <link rel="stylesheet" href="DOM-Inspector-Portable/css/dom-inspector-tooltip.css">
   
   Sur le <body> :
   <body x-data="domInspector">
   
   Avant la fermeture du </body> :
   <script src="DOM-Inspector-Portable/dependencies/alpinejs/alpine.min.js" defer></script>
   <script src="DOM-Inspector-Portable/js/dom-inspector-tooltip.js"></script>

3. TESTEZ en ouvrant votre page - le bouton "🔍 Inspecteur DOM" doit apparaître

C'est tout ! Le DOM Inspector est maintenant intégré et fonctionnel.

═══════════════════════════════════════════════════════════════════
-->
