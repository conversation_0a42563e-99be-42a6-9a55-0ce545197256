import { UserNotification } from '../types/notifications';

/**
 * Service d'envoi d'emails pour les notifications FloraSynth
 * Utilise Netlify Forms pour l'envoi d'emails
 */
class EmailService {
  private readonly netlifyFormEndpoint = '/.netlify/functions/send-notification-email';

  /**
   * Envoie un email de notification à l'utilisateur
   */
  async sendNotificationEmail(
    userEmail: string,
    userName: string,
    notification: UserNotification
  ): Promise<boolean> {
    try {
      const emailData = {
        to: userEmail,
        subject: this.formatEmailSubject(notification),
        html: this.formatEmailContent(userName, notification),
        priority: notification.priority
      };

      const response = await fetch(this.netlifyFormEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData)
      });

      if (response.ok) {
        console.log(`✅ Email envoyé avec succès à ${userEmail}`);
        return true;
      } else {
        console.error(`❌ Erreur lors de l'envoi de l'email: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'envoi de l\'email:', error);
      return false;
    }
  }

  /**
   * Formate le sujet de l'email selon la priorité
   */
  private formatEmailSubject(notification: UserNotification): string {
    const priorityPrefix = {
      urgent: '🚨 URGENT',
      high: '⚠️ IMPORTANT',
      medium: '📋',
      low: '💡'
    };

    const prefix = priorityPrefix[notification.priority] || '📋';
    return `${prefix} FloraSynth - ${notification.title}`;
  }

  /**
   * Formate le contenu HTML de l'email
   */
  private formatEmailContent(userName: string, notification: UserNotification): string {
    const priorityColors = {
      urgent: '#ef4444',
      high: '#f97316',
      medium: '#3b82f6',
      low: '#10b981'
    };

    const priorityColor = priorityColors[notification.priority] || '#3b82f6';
    const priorityText = {
      urgent: 'URGENT',
      high: 'IMPORTANT',
      medium: 'NORMAL',
      low: 'INFO'
    };

    return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification FloraSynth</title>
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #d385f5 0%, #a364f7 100%); padding: 30px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">🌿 FloraSynth</h1>
            <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">Votre assistant intelligent pour le soin des plantes</p>
        </div>

        <!-- Content -->
        <div style="padding: 30px 20px;">
            <div style="margin-bottom: 20px;">
                <p style="color: #374151; font-size: 16px; margin: 0 0 10px 0;">Bonjour <strong>${userName}</strong>,</p>
                <p style="color: #6b7280; font-size: 14px; margin: 0;">Vous avez reçu une nouvelle notification de votre jardin intelligent.</p>
            </div>

            <!-- Notification Card -->
            <div style="background-color: #f9fafb; border-left: 4px solid ${priorityColor}; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                    <span style="background-color: ${priorityColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; margin-right: 10px;">
                        ${priorityText[notification.priority]}
                    </span>
                    <span style="color: #6b7280; font-size: 12px;">
                        ${notification.createdAt.toDate().toLocaleDateString('fr-FR', { 
                          weekday: 'long', 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                    </span>
                </div>
                
                <h2 style="color: #1f2937; font-size: 18px; margin: 0 0 12px 0; font-weight: 600;">
                    ${notification.title}
                </h2>
                
                <p style="color: #4b5563; font-size: 14px; line-height: 1.6; margin: 0;">
                    ${notification.message}
                </p>
            </div>

            <!-- Action Button -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://florasynth.netlify.app/notifications" 
                   style="display: inline-block; background: linear-gradient(135deg, #d385f5 0%, #a364f7 100%); color: white; text-decoration: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; font-size: 14px;">
                    📱 Voir dans l'application
                </a>
            </div>

            <!-- Tips -->
            <div style="background-color: #ecfdf5; border: 1px solid #d1fae5; border-radius: 8px; padding: 16px; margin: 20px 0;">
                <h3 style="color: #065f46; font-size: 14px; margin: 0 0 8px 0; font-weight: 600;">💡 Conseil FloraSynth</h3>
                <p style="color: #047857; font-size: 13px; margin: 0; line-height: 1.5;">
                    Pour de meilleurs résultats, consultez régulièrement vos notifications et suivez les recommandations de notre IA Gemini.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #f9fafb; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 12px; margin: 0 0 8px 0;">
                Cet email a été envoyé automatiquement par FloraSynth
            </p>
            <p style="color: #9ca3af; font-size: 11px; margin: 0;">
                Si vous ne souhaitez plus recevoir ces notifications, vous pouvez les désactiver dans les paramètres de l'application.
            </p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Envoie un email de résumé quotidien
   */
  async sendDailySummary(
    userEmail: string,
    userName: string,
    notifications: UserNotification[],
    pendingActions: number
  ): Promise<boolean> {
    try {
      const emailData = {
        to: userEmail,
        subject: `🌿 FloraSynth - Résumé quotidien de votre jardin`,
        html: this.formatDailySummaryContent(userName, notifications, pendingActions)
      };

      const response = await fetch(this.netlifyFormEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData)
      });

      return response.ok;
    } catch (error) {
      console.error('❌ Erreur lors de l\'envoi du résumé quotidien:', error);
      return false;
    }
  }

  /**
   * Formate le contenu du résumé quotidien
   */
  private formatDailySummaryContent(
    userName: string,
    notifications: UserNotification[],
    pendingActions: number
  ): string {
    const urgentNotifications = notifications.filter(n => n.priority === 'urgent' || n.priority === 'high');
    
    return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Résumé quotidien FloraSynth</title>
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #d385f5 0%, #a364f7 100%); padding: 30px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">🌿 Résumé Quotidien</h1>
            <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">
                ${new Date().toLocaleDateString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
            </p>
        </div>

        <!-- Content -->
        <div style="padding: 30px 20px;">
            <p style="color: #374151; font-size: 16px; margin: 0 0 20px 0;">Bonjour <strong>${userName}</strong>,</p>
            
            <!-- Stats -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
                <div style="background-color: #fef3c7; border-radius: 8px; padding: 16px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #92400e;">${notifications.length}</div>
                    <div style="font-size: 12px; color: #78350f;">Notifications</div>
                </div>
                <div style="background-color: #dbeafe; border-radius: 8px; padding: 16px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #1e40af;">${pendingActions}</div>
                    <div style="font-size: 12px; color: #1e3a8a;">Actions en attente</div>
                </div>
            </div>

            ${urgentNotifications.length > 0 ? `
            <!-- Urgent Notifications -->
            <div style="background-color: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; margin: 20px 0;">
                <h3 style="color: #dc2626; font-size: 16px; margin: 0 0 12px 0;">⚠️ Actions urgentes (${urgentNotifications.length})</h3>
                ${urgentNotifications.slice(0, 3).map(notif => `
                    <div style="margin-bottom: 8px; padding: 8px; background-color: white; border-radius: 4px;">
                        <div style="font-weight: 600; color: #374151; font-size: 14px;">${notif.title}</div>
                        <div style="color: #6b7280; font-size: 12px;">${notif.message.substring(0, 100)}...</div>
                    </div>
                `).join('')}
            </div>
            ` : ''}

            <!-- Action Button -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://florasynth.netlify.app/notifications" 
                   style="display: inline-block; background: linear-gradient(135deg, #d385f5 0%, #a364f7 100%); color: white; text-decoration: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; font-size: 14px;">
                    📱 Voir toutes les notifications
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #f9fafb; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
                Résumé automatique envoyé par FloraSynth
            </p>
        </div>
    </div>
</body>
</html>`;
  }
}

export const emailService = new EmailService();
