# Script de déploiement complet
# Usage: .\scripts\deploy.ps1 "message de commit"

param(
    [Parameter(Mandatory=$true)]
    [string]$CommitMessage
)

Write-Host "🚀 Déploiement FloraSynth" -ForegroundColor Cyan
Write-Host "📝 Message: $CommitMessage" -ForegroundColor White

# Vérifier qu'on est sur master
$currentBranch = git branch --show-current
if ($currentBranch -ne "master") {
    Write-Host "❌ Erreur: Vous devez être sur la branche master" -ForegroundColor Red
    exit 1
}

# Ajouter tous les fichiers modifiés
Write-Host "📁 Ajout des fichiers modifiés..." -ForegroundColor Yellow
git add .

# Vérifier s'il y a des changements à commiter
$status = git status --porcelain
if (-not $status) {
    Write-Host "ℹ️ Aucun changement à commiter" -ForegroundColor Blue
} else {
    # Commiter les changements
    Write-Host "💾 Commit des changements..." -ForegroundColor Yellow
    git commit -m $CommitMessage
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Erreur lors du commit" -ForegroundColor Red
        exit 1
    }
}

# Pousser vers master
Write-Host "📤 Push vers origin/master..." -ForegroundColor Yellow
git push origin master

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Erreur lors du push" -ForegroundColor Red
    exit 1
}

# Synchroniser vers main pour Netlify
Write-Host "🔄 Synchronisation vers main (Netlify)..." -ForegroundColor Yellow
git push origin master:main

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Erreur lors de la synchronisation vers main" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Déploiement réussi!" -ForegroundColor Green
Write-Host "🌐 Netlify va déployer automatiquement" -ForegroundColor Cyan
Write-Host "⏱️ Le déploiement prend généralement 2-3 minutes" -ForegroundColor Blue
