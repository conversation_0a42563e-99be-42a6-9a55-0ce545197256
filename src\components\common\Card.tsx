
import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../hooks/useAuth';

interface CardProps {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  showUserAvatar?: boolean; // Nouvelle prop pour afficher l'avatar utilisateur
}

export const Card: React.FC<CardProps> = ({
  children,
  className = '',
  onClick,
  showUserAvatar = false
}) => {
  const { user } = useAuth();

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      variants={cardVariants}
      className={`bg-[#1c1a31] p-6 rounded-2xl shadow-lg relative ${onClick ? 'cursor-pointer transition-all duration-300 hover:shadow-2xl hover:shadow-[#a364f7]/20' : ''} ${className}`}
      onClick={onClick}
    >
      {/* Avatar utilisateur en vignette si demandé */}
      {showUserAvatar && user && (
        <div className="absolute top-4 right-4 w-10 h-10 rounded-full overflow-hidden border-2 border-[#d385f5] shadow-lg">
          {user.photoURL ? (
            <img
              src={user.photoURL}
              alt={user.displayName || 'Avatar utilisateur'}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-[#d385f5] to-[#a364f7] flex items-center justify-center text-white font-semibold text-sm">
              {(user.displayName || user.email || 'U').charAt(0).toUpperCase()}
            </div>
          )}
        </div>
      )}
      {children}
    </motion.div>
  );
};
