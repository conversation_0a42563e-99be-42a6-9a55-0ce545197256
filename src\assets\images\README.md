# Dossier Images - FloraSynth

## Structure des Dossiers

```
src/assets/images/
├── backgrounds/          # Fonds d'écran et wallpapers
│   ├── main/            # Fonds d'écran principaux
│   ├── auth/            # Fonds pour pages d'authentification
│   ├── dashboard/       # Fonds pour tableau de bord
│   └── features/        # Fonds spécifiques aux fonctionnalités
├── icons/               # Icônes et logos
├── illustrations/       # Illustrations et graphiques
└── ui/                  # Images d'interface utilisateur
```

## Formats Recommandés

### Wallpapers/Fonds d'écran
- **Format :** JPG ou WebP pour les photos, PNG pour les illustrations
- **Résolutions recommandées :**
  - Desktop : 1920x1080, 2560x1440
  - Mobile : 375x812, 414x896
  - Tablet : 768x1024, 1024x768
- **Taille max :** 500KB par image (optimisation importante)

### Icônes
- **Format :** SVG (vectoriel) ou PNG haute résolution
- **Tailles :** 16x16, 24x24, 32x32, 48x48, 64x64

## Utilisation dans le Code

### Import d'images
```typescript
// Import direct
import backgroundImage from '@/assets/images/backgrounds/main/nature-bg.jpg';

// Utilisation dans un composant
<div 
  className="min-h-screen bg-cover bg-center"
  style={{ backgroundImage: `url(${backgroundImage})` }}
>
```

### Avec Tailwind CSS
```typescript
// Ajouter dans tailwind.config.js
module.exports = {
  theme: {
    extend: {
      backgroundImage: {
        'nature-pattern': "url('/src/assets/images/backgrounds/main/nature-bg.jpg')",
      }
    }
  }
}

// Utilisation
<div className="bg-nature-pattern bg-cover bg-center">
```

## Optimisation

- Utiliser des outils comme TinyPNG ou ImageOptim
- Préférer WebP quand possible
- Fournir plusieurs résolutions (responsive images)
- Utiliser lazy loading pour les images non critiques

## Accessibilité

- Toujours fournir un `alt` descriptif
- Assurer un contraste suffisant avec le texte
- Prévoir des fallbacks pour les images de fond
