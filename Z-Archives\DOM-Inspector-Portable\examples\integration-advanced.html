<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intégration Avancée - DOM Inspector avec Frameworks</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- DOM Inspector CSS -->
    <link rel="stylesheet" href="../css/dom-inspector-tooltip.css">
    
    <!-- Styles personnalisés -->
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            margin: 20px;
            overflow: hidden;
        }
        
        .hero-section {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .feature-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .form-floating > .form-control {
            border-radius: 15px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .form-floating > .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stats-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .carousel-item img {
            border-radius: 15px;
            object-fit: cover;
        }
        
        .modal-content {
            border-radius: 20px;
            border: none;
            overflow: hidden;
        }
        
        .modal-header {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
        }
        
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1055;
        }
        
        /* Personnalisation DOM Inspector */
        :root {
            --tooltip-bg: rgba(30, 41, 59, 0.98);
            --tooltip-border: var(--primary-color);
            --tooltip-accent: var(--success-color);
        }
        
        .dom-inspector-toggle {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) !important;
            border: none !important;
            border-radius: 50px !important;
            padding: 15px 20px !important;
            font-weight: 600 !important;
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3) !important;
        }
        
        .dom-inspector-toggle:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4) !important;
        }
    </style>
</head>

<!-- INTÉGRATION AVANCÉE : Combinaison de données Alpine.js -->
<body x-data="{ 
    ...domInspector, 
    showModal: false, 
    currentTab: 'home',
    formData: { name: '', email: '', message: '' },
    showToast: false,
    stats: { users: 1250, projects: 89, satisfaction: 98 }
}">
    
    <!-- Toast de notification -->
    <div class="toast-container">
        <div class="toast" x-show="showToast" x-transition>
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">DOM Inspector</strong>
                <button type="button" class="btn-close" @click="showToast = false"></button>
            </div>
            <div class="toast-body">
                Inspection DOM activée ! Survolez les éléments pour les analyser.
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <!-- STRATEGIC-TAG:navigation|navbar-advanced|85 -->
        <nav class="navbar navbar-expand-lg navbar-light navbar-custom">
            <div class="container">
                <a class="navbar-brand fw-bold" href="#">
                    <i class="fas fa-search me-2 text-primary"></i>
                    DOM Inspector Pro
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="#" @click="currentTab = 'home'" 
                               :class="{ 'active': currentTab === 'home' }">Accueil</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" @click="currentTab = 'features'"
                               :class="{ 'active': currentTab === 'features' }">Fonctionnalités</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" @click="currentTab = 'demo'"
                               :class="{ 'active': currentTab === 'demo' }">Démo</a>
                        </li>
                        <li class="nav-item">
                            <button class="btn btn-primary btn-custom ms-2" @click="showModal = true">
                                <i class="fas fa-envelope me-2"></i>Contact
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- STRATEGIC-TAG:hero|hero-section|120 -->
        <section class="hero-section" x-show="currentTab === 'home'" x-transition>
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-4">
                            🔍 DOM Inspector Ultra v2.1
                        </h1>
                        <p class="lead mb-4">
                            L'outil le plus avancé pour l'inspection DOM avec localisation ultra-précise, 
                            balises stratégiques et export JSON complet.
                        </p>
                        <div class="d-flex gap-3 justify-content-center justify-content-lg-start">
                            <button class="btn btn-light btn-custom" @click="toggle(); showToast = true">
                                <i class="fas fa-play me-2"></i>Démarrer l'Inspection
                            </button>
                            <button class="btn btn-outline-light btn-custom" @click="currentTab = 'demo'">
                                <i class="fas fa-eye me-2"></i>Voir la Démo
                            </button>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="row g-3">
                            <div class="col-4">
                                <div class="stats-card">
                                    <div class="stats-number" x-text="stats.users"></div>
                                    <div>Utilisateurs</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stats-card">
                                    <div class="stats-number" x-text="stats.projects"></div>
                                    <div>Projets</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stats-card">
                                    <div class="stats-number" x-text="stats.satisfaction + '%'"></div>
                                    <div>Satisfaction</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- STRATEGIC-TAG:features|features-section|170 -->
        <section class="py-5" x-show="currentTab === 'features'" x-transition>
            <div class="container">
                <h2 class="text-center mb-5 display-5 fw-bold">Fonctionnalités Avancées</h2>
                
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center p-4">
                                <i class="fas fa-crosshairs fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">Localisation Ultra-Précise</h5>
                                <p class="card-text">Numéros de lignes estimés et balises stratégiques pour une localisation exacte dans le code source.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center p-4">
                                <i class="fas fa-tags fa-3x text-success mb-3"></i>
                                <h5 class="card-title">Balises Stratégiques</h5>
                                <p class="card-text">Système révolutionnaire de balises pour une identification instantanée des éléments.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center p-4">
                                <i class="fas fa-download fa-3x text-warning mb-3"></i>
                                <h5 class="card-title">Export JSON</h5>
                                <p class="card-text">Export complet des métadonnées en format JSON pour analyse automatisée.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- STRATEGIC-TAG:demo|demo-section|210 -->
        <section class="py-5 bg-light" x-show="currentTab === 'demo'" x-transition>
            <div class="container">
                <h2 class="text-center mb-5 display-5 fw-bold">Démonstration Interactive</h2>
                
                <!-- Carrousel Bootstrap -->
                <div id="demoCarousel" class="carousel slide mb-5" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        <div class="carousel-item active">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h4>Inspection en Temps Réel</h4>
                                    <p>Survolez n'importe quel élément pour obtenir des informations détaillées instantanément.</p>
                                </div>
                                <div class="col-md-6">
                                    <div class="p-4 bg-primary text-white rounded">
                                        <h5>Élément de Démonstration</h5>
                                        <p>Survolez cette carte pour voir l'inspecteur en action !</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="carousel-item">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h4>Raccourcis Clavier</h4>
                                    <p>Utilisez CTRL+C pour copier, CTRL+Q pour verrouiller le tooltip.</p>
                                </div>
                                <div class="col-md-6">
                                    <div class="p-4 bg-success text-white rounded">
                                        <h5>Zone Interactive</h5>
                                        <button class="btn btn-light">Bouton Test</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#demoCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#demoCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                    </button>
                </div>
                
                <!-- Accordéon Bootstrap -->
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                Comment utiliser l'inspecteur ?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Cliquez sur le bouton "🔍 Inspecteur DOM" en bas à droite, puis survolez les éléments.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                Quels sont les raccourcis disponibles ?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                CTRL+C (copier), CTRL+Q (verrouiller), CTRL+SHIFT+C (export JSON), ÉCHAP (désactiver).
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    
    <!-- Modal de Contact -->
    <div class="modal fade" x-show="showModal" x-transition tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-envelope me-2"></i>Contactez-nous
                    </h5>
                    <button type="button" class="btn-close btn-close-white" @click="showModal = false"></button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="showModal = false; showToast = true">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="modalName" x-model="formData.name" placeholder="Nom">
                            <label for="modalName">Nom complet</label>
                        </div>
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="modalEmail" x-model="formData.email" placeholder="Email">
                            <label for="modalEmail">Adresse email</label>
                        </div>
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="modalMessage" x-model="formData.message" placeholder="Message" style="height: 100px"></textarea>
                            <label for="modalMessage">Votre message</label>
                        </div>
                        <button type="submit" class="btn btn-primary btn-custom w-100">
                            <i class="fas fa-paper-plane me-2"></i>Envoyer le Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Alpine.js -->
    <script src="../dependencies/alpinejs/alpine.min.js" defer></script>
    
    <!-- DOM Inspector Script -->
    <script src="../js/dom-inspector-tooltip.js"></script>
    
</body>
</html>
