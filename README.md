# 🌱 FloraSynth

**Application intelligente de soin des plantes avec IA intégrée**

FloraSynth est une application web moderne qui aide les passionnés de plantes à prendre soin de leurs végétaux grâce à l'intelligence artificielle Google Gemini et un système d'archivage automatique pour l'apprentissage continu.

## ✨ Fonctionnalités

### 🔍 Diagnostic IA
- **Analyse par photos** : Uploadez 3 photos de votre plante (vue d'ensemble, rapprochée, macro)
- **Diagnostic Gemini** : IA spécialisée en botanique pour identifier maladies et carences
- **Recommandations personnalisées** : Traitements adaptés avec dosages précis

### 📱 Gestion des plantes
- **Tableau de bord intuitif** : Vue d'ensemble de toutes vos plantes
- **Fiches détaillées** : Informations complètes pour chaque plante
- **Historique des soins** : Suivi des arrosages, fertilisations et traitements

### 📅 Planification intelligente
- **Calendrier des soins** : Planification automatique des tâches
- **Notifications préventives** : Rappels personnalisés
- **Suivi des cycles** : Adaptation aux besoins saisonniers

### 📊 Archives et apprentissage
- **Archivage automatique** : Sauvegarde annuelle des données (1er janvier)
- **Accès Gemini** : IA apprend de vos données pour améliorer les diagnostics
- **Statistiques détaillées** : Analyse de vos pratiques de jardinage

### 📝 Journal de bord
- **Observations quotidiennes** : Notez l'évolution de vos plantes
- **Export CSV** : Sauvegarde de vos données
- **Recherche avancée** : Retrouvez facilement vos notes

## 🛠️ Technologies

### Frontend
- **React 18+** avec TypeScript
- **Vite.js** pour le développement et build
- **Tailwind CSS** pour le styling
- **Framer Motion** pour les animations
- **React Router** pour la navigation

### Backend & Services
- **Firebase** (Authentication, Firestore, Storage)
- **Google Gemini AI** pour les diagnostics
- **Netlify** pour le déploiement

### Outils de développement
- **ESLint** et **Prettier** pour la qualité du code
- **TypeScript** strict (pas de `any`)
- **Git** avec hooks de pré-commit

## 🚀 Installation

### Prérequis
- Node.js 18+
- npm ou yarn
- Compte Firebase
- Clé API Google Gemini

### Configuration locale

1. **Cloner le projet**
```bash
git clone [URL_DU_REPO]
cd florasynth
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configuration Firebase**
```bash
# Créer le fichier .env.local
cp .env.example .env.local

# Ajouter vos clés Firebase et Gemini
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_GEMINI_API_KEY=your_gemini_key
```

4. **Lancer en développement**
```bash
npm run dev
```

## 📦 Déploiement

### Netlify (Recommandé)

1. **Build de production**
```bash
npm run build
```

2. **Configuration automatique**
Le fichier `netlify.toml` configure automatiquement :
- Redirections SPA
- Headers de sécurité
- Cache des assets
- Variables d'environnement

3. **Déploiement**
- Connecter le repo GitHub à Netlify
- Les variables d'environnement sont configurées dans l'interface Netlify
- Déploiement automatique à chaque push

## 🔒 Sécurité

### Variables d'environnement
- ✅ Clés API stockées dans `.env.local` (non versionné)
- ✅ Configuration Netlify pour les variables de production
- ✅ Dossiers sensibles exclus du Git (`.gitignore`)

### Dossiers exclus
- `Z-Archives/` : Archives de développement
- `_Framework d'Instructions/` : Documentation interne
- `.env*` : Variables d'environnement

## 🧪 Tests

```bash
# Tests unitaires
npm run test

# Tests e2e
npm run test:e2e

# Coverage
npm run test:coverage
```

## 📚 Documentation

- **Architecture** : Voir `/docs/architecture.md`
- **API** : Voir `/docs/api.md`
- **Composants** : Voir `/docs/components.md`

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🙏 Remerciements

- **Google Gemini** pour l'IA de diagnostic
- **Firebase** pour l'infrastructure backend
- **Netlify** pour l'hébergement
- **Communauté React** pour l'écosystème

---

**Développé avec ❤️ pour les amoureux des plantes** 🌿
