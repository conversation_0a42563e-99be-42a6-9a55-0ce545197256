import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Alert<PERSON>riangle, TestTube, Trash2, CheckCircle } from 'lucide-react';
import { useAuth } from '../../../hooks/useAuth';
import { testArchiveService } from '../../../services/testArchiveService';
import { useArchive } from '../../../hooks/useArchive';

/**
 * Composant de test pour créer et gérer des archives fictives
 * À utiliser uniquement pour le débogage
 */
const TestArchiveManager: React.FC = () => {
  const { user } = useAuth();
  const { archives, loadArchives } = useArchive();
  const [isCreating, setIsCreating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [messageType, setMessageType] = useState<'success' | 'error'>('success');

  const showMessage = (msg: string, type: 'success' | 'error' = 'success') => {
    setMessage(msg);
    setMessageType(type);
    setTimeout(() => setMessage(null), 5000);
  };

  /**
   * Crée une archive fictive pour tester
   */
  const handleCreateTestArchive = async () => {
    if (!user) return;

    setIsCreating(true);
    try {
      const result = await testArchiveService.createTestArchive(user.uid, 2023);
      showMessage(result, 'success');
      
      // Recharger les archives pour voir la nouvelle archive
      await loadArchives();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      showMessage(`Erreur: ${errorMessage}`, 'error');
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Supprime l'archive de test
   */
  const handleDeleteTestArchive = async () => {
    if (!user) return;

    setIsDeleting(true);
    try {
      await testArchiveService.deleteTestArchive(user.uid, 2023);
      showMessage('Archive de test supprimée avec succès', 'success');
      
      // Recharger les archives
      await loadArchives();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      showMessage(`Erreur: ${errorMessage}`, 'error');
    } finally {
      setIsDeleting(false);
    }
  };

  // Vérifier s'il y a des archives de test
  const testArchives = archives.filter(archive => 
    testArchiveService.isTestArchive(archive)
  );

  return (
    <Card className="bg-[#1c1a31] border-yellow-500 border-2">
      <CardHeader>
        <CardTitle className="text-yellow-400 flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          Gestionnaire d'Archives de Test
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-yellow-400 border-yellow-400">
            🧪 Mode Débogage
          </Badge>
          <Badge variant="outline" className="text-orange-400 border-orange-400">
            ⚠️ Temporaire
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Message de statut */}
        {message && (
          <div className={`p-3 rounded-lg flex items-center gap-2 ${
            messageType === 'success' 
              ? 'bg-green-500/10 border border-green-500 text-green-400'
              : 'bg-red-500/10 border border-red-500 text-red-400'
          }`}>
            {messageType === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertTriangle className="h-4 w-4" />
            )}
            {message}
          </div>
        )}

        {/* Description */}
        <div className="bg-[#100f1c] p-4 rounded-lg border border-gray-600">
          <h3 className="text-white font-medium mb-2">Objectif du Test</h3>
          <p className="text-gray-400 text-sm mb-3">
            Ce composant permet de créer une archive fictive avec des données de test 
            pour vérifier si les problèmes de boucle infinie viennent du système d'archives.
          </p>
          <div className="text-xs text-gray-500">
            <strong>Contenu de l'archive fictive :</strong>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>5 plantes fictives avec noms "Test"</li>
              <li>15-25 diagnostics fictifs pour l'année 2023</li>
              <li>Données Gemini simulées</li>
              <li>Marquage spécial pour identification</li>
            </ul>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={handleCreateTestArchive}
            disabled={isCreating || testArchives.length > 0}
            className="bg-yellow-600 hover:bg-yellow-700 text-black font-medium"
          >
            {isCreating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent mr-2" />
                Création...
              </>
            ) : (
              <>
                <TestTube className="h-4 w-4 mr-2" />
                Créer Archive Fictive 2023
              </>
            )}
          </Button>

          {testArchives.length > 0 && (
            <Button
              onClick={handleDeleteTestArchive}
              disabled={isDeleting}
              variant="outline"
              className="border-red-500 text-red-400 hover:bg-red-500/10"
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-400 border-t-transparent mr-2" />
                  Suppression...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Supprimer Archive Test
                </>
              )}
            </Button>
          )}
        </div>

        {/* Statut des archives de test */}
        {testArchives.length > 0 && (
          <div className="bg-green-500/10 border border-green-500 p-3 rounded-lg">
            <h4 className="text-green-400 font-medium mb-2">Archives de Test Actives</h4>
            {testArchives.map(archive => (
              <div key={archive.year} className="text-sm text-green-300">
                📁 Archive {archive.year}: {archive.totalPlants} plantes, {archive.totalDiagnostics} diagnostics
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-500/10 border border-blue-500 p-3 rounded-lg">
          <h4 className="text-blue-400 font-medium mb-2">Instructions de Test</h4>
          <ol className="text-sm text-blue-300 space-y-1 list-decimal list-inside">
            <li>Créer l'archive fictive avec le bouton ci-dessus</li>
            <li>Observer la console pour détecter d'éventuelles boucles</li>
            <li>Vérifier les performances de l'application</li>
            <li>Supprimer l'archive une fois les tests terminés</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default TestArchiveManager;
