import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, BeakerIcon } from '@heroicons/react/24/outline';
import { FERTILIZERS_DATABASE, Fertilizer } from '@/data/fertilizers';
import { DEFICIENCY_GUIDES, DeficiencyGuide } from '@/data/plant-care-guide';
import { motion } from 'framer-motion';
import { BackgroundWrapper } from '@/components/common/BackgroundWrapper';

const FertilizerGuideScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'fertilizers' | 'deficiencies'>('fertilizers');
  const [selectedFertilizer, setSelectedFertilizer] = useState<Fertilizer | null>(null);
  const [selectedDeficiency, setSelectedDeficiency] = useState<DeficiencyGuide | null>(null);

  return (
    <BackgroundWrapper backgroundKey="fertilizerGuide" overlayOpacity={0.3}>
      <div className="p-6 pt-20">
        <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center gap-3 mb-4">
            <BeakerIcon className="w-10 h-10 text-[#d385f5]" />
            <h1 className="text-4xl font-bold text-white">Guide des Engrais et Carences</h1>
          </div>
          <p className="text-[#E0E0E0] text-lg">
            Découvrez les engrais adaptés à vos plantes et apprenez à identifier et traiter les carences nutritionnelles.
          </p>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-[#1c1a31]/50 backdrop-blur-lg rounded-2xl overflow-hidden mb-8"
        >
          <div className="flex border-b border-[#3D3B5E]">
            <button
              onClick={() => {
                setActiveTab('fertilizers');
                // Fermer tous les accordéons quand on change d'onglet
                setSelectedFertilizer(null);
                setSelectedDeficiency(null);
              }}
              className={`flex-1 py-4 px-8 text-center transition-colors font-medium ${
                activeTab === 'fertilizers'
                  ? 'bg-[#d385f5] text-white'
                  : 'text-gray-400 hover:text-white hover:bg-[#2a2847]'
              }`}
            >
              Engrais Disponibles
            </button>
            <button
              onClick={() => {
                setActiveTab('deficiencies');
                // Fermer tous les accordéons quand on change d'onglet
                setSelectedFertilizer(null);
                setSelectedDeficiency(null);
              }}
              className={`flex-1 py-4 px-8 text-center transition-colors font-medium ${
                activeTab === 'deficiencies'
                  ? 'bg-[#d385f5] text-white'
                  : 'text-gray-400 hover:text-white hover:bg-[#2a2847]'
              }`}
            >
              Guide des Carences
            </button>
          </div>

          {/* Content */}
          <div className="p-8">
            {activeTab === 'fertilizers' ? (
              <motion.div
                key="fertilizers"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="space-y-6"
              >
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {FERTILIZERS_DATABASE.map((fertilizer) => (
                    <div
                      key={fertilizer.id}
                      className="bg-[#100f1c]/80 backdrop-blur-sm p-6 rounded-xl border border-[#3D3B5E] cursor-pointer hover:border-[#d385f5] transition-all duration-300 hover:shadow-lg hover:shadow-[#d385f5]/20"
                      onClick={() => {
                        // Si on clique sur l'accordéon déjà ouvert, on le ferme
                        if (selectedFertilizer?.id === fertilizer.id) {
                          setSelectedFertilizer(null);
                        } else {
                          // Sinon, on ferme tous les autres et on ouvre celui-ci
                          setSelectedFertilizer(fertilizer);
                        }
                      }}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-[#d385f5] text-lg">{fertilizer.nom}</h3>
                        {selectedFertilizer?.id === fertilizer.id ? (
                          <ChevronUpIcon className="w-5 h-5 text-gray-400" />
                        ) : (
                          <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                      <p className="text-sm text-gray-400 mb-2">{fertilizer.composition_npk}</p>
                      <div className="flex items-center gap-2 mb-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          fertilizer.type_engrais === 'Organique' 
                            ? 'bg-green-500/20 text-green-300'
                            : fertilizer.type_engrais === 'Minéral'
                            ? 'bg-blue-500/20 text-blue-300'
                            : 'bg-purple-500/20 text-purple-300'
                        }`}>
                          {fertilizer.type_engrais}
                        </span>
                        {fertilizer.agriculture_biologique_compatible && (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-emerald-500/20 text-emerald-300">
                            Bio Compatible
                          </span>
                        )}
                      </div>
                      
                      {selectedFertilizer?.id === fertilizer.id && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="mt-4 space-y-4 text-sm"
                        >
                          <div>
                            <h4 className="font-medium text-white mb-2">Description :</h4>
                            <p className="text-gray-300">{fertilizer.description_action}</p>
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-white mb-2">Plantes cibles :</h4>
                            <div className="flex flex-wrap gap-1">
                              {fertilizer.plantes_cibles_general.map((plant, i) => (
                                <span key={i} className="px-2 py-1 bg-[#1c1a31] rounded text-gray-300 text-xs">
                                  {plant}
                                </span>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-white mb-2">Dosages :</h4>
                            {fertilizer.dosages.map((dosage, i) => (
                              <div key={i} className="bg-[#1c1a31] p-3 rounded-lg mt-2">
                                <span className="font-medium text-[#d385f5]">{dosage.methode} :</span>
                                <span className="text-gray-300 ml-2">{dosage.dose}</span>
                                <p className="text-xs text-gray-400 mt-1">{dosage.instructions_application}</p>
                              </div>
                            ))}
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-white mb-2">Précautions :</h4>
                            <p className="text-orange-300 text-xs bg-orange-500/10 p-2 rounded">
                              {fertilizer.precautions_emploi}
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </div>
                  ))}
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="deficiencies"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="space-y-6"
              >
                {DEFICIENCY_GUIDES.map((guide, index) => (
                  <div
                    key={index}
                    className="bg-[#100f1c]/80 backdrop-blur-sm p-6 rounded-xl border border-[#3D3B5E] cursor-pointer hover:border-[#d385f5] transition-all duration-300 hover:shadow-lg hover:shadow-[#d385f5]/20"
                    onClick={() => {
                      // Si on clique sur l'accordéon déjà ouvert, on le ferme
                      if (selectedDeficiency?.element === guide.element) {
                        setSelectedDeficiency(null);
                      } else {
                        // Sinon, on ferme tous les autres et on ouvre celui-ci
                        setSelectedDeficiency(guide);
                      }
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-[#d385f5] text-lg">Carence en {guide.element}</h3>
                      {selectedDeficiency?.element === guide.element ? (
                        <ChevronUpIcon className="w-5 h-5 text-gray-400" />
                      ) : (
                        <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                    
                    {selectedDeficiency?.element === guide.element && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-6 grid md:grid-cols-2 gap-6 text-sm"
                      >
                        <div>
                          <h4 className="font-medium text-white mb-3 flex items-center gap-2">
                            🔍 Symptômes
                          </h4>
                          <ul className="list-disc list-inside text-gray-300 space-y-2">
                            {guide.symptoms.map((symptom, i) => (
                              <li key={i} className="leading-relaxed">{symptom}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-white mb-3 flex items-center gap-2">
                            ⚡ Causes
                          </h4>
                          <ul className="list-disc list-inside text-gray-300 space-y-2">
                            {guide.causes.map((cause, i) => (
                              <li key={i} className="leading-relaxed">{cause}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-white mb-3 flex items-center gap-2">
                            💊 Solutions
                          </h4>
                          <ul className="list-disc list-inside text-green-300 space-y-2">
                            {guide.solutions.map((solution, i) => (
                              <li key={i} className="leading-relaxed">{solution}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-white mb-3 flex items-center gap-2">
                            🛡️ Prévention
                          </h4>
                          <ul className="list-disc list-inside text-blue-300 space-y-2">
                            {guide.prevention.map((prevention, i) => (
                              <li key={i} className="leading-relaxed">{prevention}</li>
                            ))}
                          </ul>
                        </div>
                      </motion.div>
                    )}
                  </div>
                ))}
              </motion.div>
            )}
          </div>
        </motion.div>
        </div>
      </div>
    </BackgroundWrapper>
  );
};

export default FertilizerGuideScreen;
