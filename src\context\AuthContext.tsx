import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { User } from 'firebase/auth';
import { auth } from '@/services/api';
import { logger } from '@/utils/logger';

export interface AuthContextType {
  user: User | null;
  loading: boolean;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    logger.debug('Initialisation de l\'AuthContext', {
      component: 'AuthContext',
      action: 'Init'
    });

    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      logger.auth.stateChange(!!currentUser);

      setUser(currentUser);
      setLoading(false);

      // Log détaillé pour débugger
      if (currentUser) {
        logger.info('Utilisateur connecté dans AuthContext', {
          component: 'AuthContext',
          action: 'UserConnected'
        });
      } else {
        logger.debug('Utilisateur déconnecté dans AuthContext', {
          component: 'AuthContext',
          action: 'UserDisconnected'
        });
      }
    });

    return () => {
      logger.debug('Nettoyage de l\'AuthContext', {
        component: 'AuthContext',
        action: 'Cleanup'
      });
      unsubscribe();
    };
  }, []);

  const value = {
    user,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
