import React from 'react';
import { useResponsiveBackground } from '@/hooks/useResponsiveBackground';
import { getBackgroundConfig, BackgroundImageKey } from '@/utils/backgroundImages';

interface BackgroundWrapperProps {
  /** Clé de l'image de fond à utiliser */
  backgroundKey: BackgroundImageKey;
  /** Contenu à afficher */
  children: React.ReactNode;
  /** Opacité de l'overlay (par défaut: 0.3) */
  overlayOpacity?: number;
  /** Classe CSS supplémentaire pour le conteneur */
  className?: string;
  /** Activer le scroll interne (par défaut: true) */
  enableScroll?: boolean;
}

/**
 * Composant wrapper pour les pages avec image de fond responsive
 * Garantit qu'aucun ascenseur n'apparaît sur la page
 */
export const BackgroundWrapper: React.FC<BackgroundWrapperProps> = ({
  backgroundKey,
  children,
  overlayOpacity = 0.3,
  className = '',
  enableScroll = true
}) => {
  // Configuration de l'image de fond
  const backgroundConfig = getBackgroundConfig(backgroundKey);
  const { backgroundStyles, isReady } = useResponsiveBackground(backgroundConfig);

  return (
    <div 
      className={`fixed inset-0 overflow-hidden ${className}`}
      style={backgroundStyles}
    >
      {/* Overlay pour améliorer la lisibilité */}
      <div 
        className="absolute inset-0 bg-black z-10"
        style={{ opacity: overlayOpacity }}
      />
      
      {/* Contenu principal */}
      <div className={`relative z-20 h-full ${enableScroll ? 'overflow-y-auto' : 'overflow-hidden'}`}>
        {children}
      </div>
    </div>
  );
};
