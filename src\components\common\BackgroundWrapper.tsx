import React from 'react';
import { useResponsiveBackground } from '@/hooks/useResponsiveBackground';
import { getBackgroundConfig, BackgroundImageKey } from '@/utils/backgroundImages';

interface BackgroundWrapperProps {
  /** Clé de l'image de fond à utiliser */
  backgroundKey: BackgroundImageKey;
  /** Contenu à afficher */
  children: React.ReactNode;
  /** Opacité de l'overlay (par défaut: 0.3) */
  overlayOpacity?: number;
  /** Classe CSS supplémentaire pour le conteneur */
  className?: string;
  /** Activer le scroll interne (par défaut: true) */
  enableScroll?: boolean;
  /** Forcer le scroll en haut au montage (par défaut: false) */
  scrollToTop?: boolean;
}

/**
 * Composant wrapper pour les pages avec image de fond responsive
 * Garantit qu'aucun ascenseur n'apparaît sur la page
 */
export const BackgroundWrapper: React.FC<BackgroundWrapperProps> = ({
  backgroundKey,
  children,
  overlayOpacity = 0.3,
  className = '',
  enableScroll = true,
  scrollToTop = false
}) => {
  // Configuration de l'image de fond
  const backgroundConfig = getBackgroundConfig(backgroundKey);
  const { backgroundStyles, isReady } = useResponsiveBackground(backgroundConfig);

  // Vérifier si un flou doit être appliqué
  const shouldBlur = (backgroundConfig as any).blur === true;

  // Forcer le scroll en haut si demandé
  React.useEffect(() => {
    if (scrollToTop && enableScroll) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [scrollToTop, enableScroll]);

  return (
    <div
      className={`fixed inset-0 overflow-hidden ${className}`}
      style={backgroundStyles}
    >
      {/* Couche de flou si nécessaire */}
      {shouldBlur && (
        <div
          className="absolute inset-0 z-5"
          style={{
            ...backgroundStyles,
            filter: 'blur(2px)',
            transform: 'scale(1.1)' // Évite les bordures noires du flou
          }}
        />
      )}

      {/* Overlay pour améliorer la lisibilité */}
      <div
        className="absolute inset-0 bg-black z-10"
        style={{ opacity: overlayOpacity }}
      />

      {/* Contenu principal */}
      <div className={`relative z-20 h-full ${enableScroll ? 'overflow-y-auto' : 'overflow-hidden'}`}>
        {children}
      </div>
    </div>
  );
};
