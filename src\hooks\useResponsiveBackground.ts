import { useState, useEffect } from 'react';

/**
 * Interface pour les options d'image de fond responsive
 */
export interface ResponsiveBackgroundOptions {
  /** URL de l'image de fond */
  imageUrl: string;
  /** Position de l'image (par défaut: 'center') */
  position?: 'center' | 'left' | 'right' | 'top' | 'bottom' | 'left-top' | 'right-top' | 'left-bottom' | 'right-bottom';
  /** Mode de redimensionnement (par défaut: 'cover') */
  size?: 'cover' | 'contain' | 'auto' | '100% 100%';
  /** Répétition de l'image (par défaut: 'no-repeat') */
  repeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';
  /** Opacité de l'image (par défaut: 1) */
  opacity?: number;
  /** Couleur de fond de secours */
  fallbackColor?: string;
  /** Activer le mode responsive adaptatif */
  adaptive?: boolean;
}

/**
 * Interface pour les styles CSS générés
 */
export interface BackgroundStyles {
  backgroundImage: string;
  backgroundPosition: string;
  backgroundSize: string;
  backgroundRepeat: string;
  backgroundAttachment: string;
  minHeight: string;
  width: string;
  height: string;
  opacity?: number;
  backgroundColor?: string;
}

/**
 * Hook personnalisé pour gérer les images de fond responsive
 * Adapte automatiquement l'image selon la taille de l'écran
 */
export const useResponsiveBackground = (options: ResponsiveBackgroundOptions) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1920,
    height: typeof window !== 'undefined' ? window.innerHeight : 1080
  });

  // Surveiller les changements de taille d'écran
  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Précharger l'image
  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setIsLoaded(true);
      setHasError(false);
    };
    img.onerror = () => {
      setHasError(true);
      setIsLoaded(false);
    };
    img.src = options.imageUrl;
  }, [options.imageUrl]);

  /**
   * Détermine la position optimale selon la taille d'écran
   */
  const getOptimalPosition = (): string => {
    if (!options.adaptive) {
      return getPositionValue(options.position || 'center');
    }

    // Logique adaptative selon le ratio d'écran
    const aspectRatio = screenSize.width / screenSize.height;
    
    if (aspectRatio > 1.8) {
      // Écran très large (ultrawide)
      return options.position === 'left' ? 'left center' : 'center center';
    } else if (aspectRatio < 0.8) {
      // Écran portrait (mobile)
      return 'center top';
    } else {
      // Écran standard
      return getPositionValue(options.position || 'center');
    }
  };

  /**
   * Détermine la taille optimale selon la taille d'écran
   */
  const getOptimalSize = (): string => {
    if (!options.adaptive) {
      return options.size || 'cover';
    }

    const aspectRatio = screenSize.width / screenSize.height;
    
    if (aspectRatio > 1.8) {
      // Écran très large - étirer pour éviter les bandes noires
      return '100% 100%';
    } else if (aspectRatio < 0.8) {
      // Écran portrait - couvrir entièrement
      return 'cover';
    } else {
      return options.size || 'cover';
    }
  };

  /**
   * Convertit les positions nommées en valeurs CSS
   */
  const getPositionValue = (position: string): string => {
    const positions: Record<string, string> = {
      'center': 'center center',
      'left': 'left center',
      'right': 'right center',
      'top': 'center top',
      'bottom': 'center bottom',
      'left-top': 'left top',
      'right-top': 'right top',
      'left-bottom': 'left bottom',
      'right-bottom': 'right bottom'
    };
    return positions[position] || 'center center';
  };

  /**
   * Génère les styles CSS pour l'image de fond
   */
  const getBackgroundStyles = (): BackgroundStyles => {
    const baseStyles: BackgroundStyles = {
      backgroundImage: isLoaded && !hasError ? `url(${options.imageUrl})` : 'none',
      backgroundPosition: getOptimalPosition(),
      backgroundSize: getOptimalSize(),
      backgroundRepeat: options.repeat || 'no-repeat',
      backgroundAttachment: 'fixed',
      minHeight: '100vh',
      maxHeight: '100vh', // Empêche le débordement vertical
      width: '100vw',
      maxWidth: '100vw', // Empêche le débordement horizontal
      height: '100vh',
      overflow: 'hidden' // Masque tout débordement
    };

    // Ajouter l'opacité si spécifiée
    if (options.opacity !== undefined) {
      baseStyles.opacity = options.opacity;
    }

    // Ajouter la couleur de fond de secours
    if (options.fallbackColor) {
      baseStyles.backgroundColor = options.fallbackColor;
    }

    return baseStyles;
  };

  /**
   * Génère les classes Tailwind CSS pour l'image de fond
   */
  const getTailwindClasses = (): string => {
    const classes = [
      'min-h-screen',
      'w-full',
      'bg-no-repeat',
      'bg-fixed'
    ];

    // Position
    const position = getOptimalPosition();
    if (position.includes('center')) classes.push('bg-center');
    else if (position.includes('left')) classes.push('bg-left');
    else if (position.includes('right')) classes.push('bg-right');
    if (position.includes('top')) classes.push('bg-top');
    else if (position.includes('bottom')) classes.push('bg-bottom');

    // Taille
    const size = getOptimalSize();
    if (size === 'cover') classes.push('bg-cover');
    else if (size === 'contain') classes.push('bg-contain');

    return classes.join(' ');
  };

  return {
    isLoaded,
    hasError,
    screenSize,
    backgroundStyles: getBackgroundStyles(),
    tailwindClasses: getTailwindClasses(),
    isReady: isLoaded && !hasError
  };
};
