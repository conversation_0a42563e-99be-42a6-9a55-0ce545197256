# **Schéma d'Architecture : FLEXODIV-SITE**

Voici une représentation schématique de l'arborescence du projet, montrant l'imbrication et le rôle de chaque composant principal.

FLEXODIV-SITE/
├── .gitignore              \# Fichiers et dossiers à ignorer par le contrôle de version Git.
├── netlify.toml            \# Fichier de configuration pour le déploiement sur Netlify.
├── package.json            \# Définit les métadonnées du projet et liste les dépendances (npm).
├── package-lock.json       \# Enregistre les versions exactes de chaque dépendance installée.
├── vite.config.js          \# Fichier de configuration pour l'outil de build Vite.js.
├── README.md               \# Documentation générale du projet (mise à jour 2025-06-22).
├── 404.html                \# Page affichée lorsqu'une ressource n'est pas trouvée.
├── index.html              \# Racine du site, la page d'accueil (liens corrigés).
├── favicon.ico             \# Icône du site affichée dans l'onglet du navigateur.
│
├── Backup-Website/         \# \--- Sauvegarde de l'Ancienne Version \---
│   ├── index.html          \# Ancienne version de la page d'accueil.
│   ├── portfolio-4-columns.html \# Ancienne page portfolio.
│   ├── blog.html           \# Ancienne page blog.
│   ├── contact.html        \# Ancienne page contact.
│   └── ... (autres fichiers de sauvegarde)
│
├── Dev-Notes/              \# \--- Notes et Documentation de Développement \---
│   ├── Archives/           \# Archives des notes de développement.
│   ├── Documentation/      \# Documentation technique détaillée.
│   └── Notes-MD/           \# Notes en format Markdown.
│
├── Dom-Elements-website/   \# \--- Documentation Architecture du Site \---
│   ├── FLEXODIV-SITE.md    \# Ce fichier - Schéma d'architecture (mise à jour 2025-06-22).
│   └── Architecture-Site-Portfolio.md \# Documentation architecture portfolio.
│  
├── css/                    \# \--- Dossier des Styles (Apparence Visuelle) \---  
│   ├── animations.css      \# Styles dédiés aux animations CSS.  
│   ├── bootstrap.min.css   \# Framework de base pour le design responsive et les composants.  
│   ├── main.css            \# Ta feuille de style principale pour la personnalisation.  
│   ├── magnific-popup.css  \# Styles pour la galerie d'images en pop-up.  
│   ├── normalize.css       \# Assure un rendu cohérent des styles sur tous les navigateurs.  
│   ├── owl.carousel.css    \# Styles pour les carrousels/sliders.  
│   └── perfect-scrollbar.css \# Styles pour des barres de défilement personnalisées.  
│  
├── js/                     \# \--- Dossier des Scripts (Comportement et Interactivité) \---  
│   ├── jquery-3.5.1.min.js \# Bibliothèque JS de base pour la manipulation du DOM.  
│   ├── bootstrap.min.js    \# Scripts nécessaires au fonctionnement des composants Bootstrap.  
│   ├── masonry.pkgd.min.js \# Plugin pour créer des mises en page en grille "maçonnée".  
│   ├── jquery.shuffle.min.js \# Plugin pour filtrer et réorganiser les éléments d'une grille.  
│   └── ... (et autres plugins jQuery pour les carrousels, pop-ups, etc.)  
│  
├── img/                    \# \--- Dossier des Images \---  
│   ├── hp\_bg\_1.jpg         \# Image de fond principale.  
│   ├── main\_photo.jpg      \# Photo principale du site.  
│   └── ... (sous-dossiers pour clients, portfolio, etc.)  
│  
├── pages/                  \# \--- Dossier des Pages HTML du Site (Structure Réorganisée) \---
│   ├── portfolio/          \# Pages du portfolio
│   │   ├── portfolio-4-columns.html \# Page principale du portfolio (4 colonnes).
│   │   ├── portfolio-project-1.html \# Page de détail pour un projet spécifique.
│   │   ├── portfolio-project-2.html \# Page de détail pour un autre projet.
│   │   └── projects/       \# Dossier des projets individuels
│   │       ├── DeveloperInspectorMode_Module/ \# Projet d'inspection DOM (hors stack).
│   │       ├── Advisor-Nutrition/ \# Projet application nutrition.
│   │       ├── budgetwise/    \# Projet application budget.
│   │       └── ...
│   │
│   ├── blog/               \# Pages du blog
│   │   ├── blog.html       \# Page principale du blog.
│   │   ├── blog-post-1.html \# Article de blog spécifique.
│   │   └── ...
│   │
│   ├── contact_form/       \# Page et formulaire de contact
│   │   ├── contact.html    \# La page de contact principale.
│   │   └── contact_form.php \# Script PHP côté serveur pour traiter l'envoi du formulaire.
│   │
│   ├── CV/                 \# Section CV/Resume
│   │   └── ... (fichiers CV)
│   │
│   └── pages-archives/     \# Archives des anciennes pages
│       └── ... (pages archivées)
│  
└── node\_modules/           \# Dossier généré par npm contenant toutes les dépendances téléchargées.

---

