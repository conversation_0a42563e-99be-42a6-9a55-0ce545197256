# **13_CORRECTION_ERREURS_NETLIFY_FIREBASE.MD - RÉSOLUTION DES ERREURS DE DÉPLOIEMENT**

## **PROBLÈMES IDENTIFIÉS**

### **1. Erreurs CORS (Cross-Origin-Opener-Policy)**
```
Cross-Origin-Opener-Policy policy would block the window.closed call.
```

**Cause :** Politique CORS trop restrictive dans `netlify.toml`
**Solution :** Changement de `same-origin-allow-popups` vers `unsafe-none`

### **2. Erreurs Firestore (Permission Denied)**
```
FirebaseError: [code=permission-denied]. Missing or insufficient permissions.
```

**Cause :** Document utilisateur non créé dans Firestore après l'authentification
**Solution :** Ajout de la fonction `createOrUpdateUserDocument`

### **3. Avertissement Google Photos**
```
WARN (AuthenticationSlowLogging) <app de connexion anormalement long détecté>
```

**Cause :** Connexion lente à l'API Google Photos
**Solution :** Optimisation du processus d'authentification

---

## **CORRECTIONS APPLIQUÉES**

### **1. Modification netlify.toml**

**Avant :**
```toml
Cross-Origin-Opener-Policy = "same-origin-allow-popups"
Cross-Origin-Embedder-Policy = "unsafe-none"
```

**Après :**
```toml
Cross-Origin-Opener-Policy = "unsafe-none"
Cross-Origin-Embedder-Policy = "unsafe-none"
```

### **2. Ajout de la fonction createOrUpdateUserDocument**

**Fichier :** `src/services/api.ts`

```typescript
// Fonction pour créer ou mettre à jour le document utilisateur dans Firestore
export const createOrUpdateUserDocument = async (user: any) => {
  try {
    const userRef = doc(db, 'users', user.uid);
    const userSnap = await getDoc(userRef);

    const userData = {
      email: user.email,
      displayName: user.displayName || user.email?.split('@')[0] || 'Utilisateur',
      photoURL: user.photoURL || null,
      lastLoginAt: new Date(),
    };

    if (!userSnap.exists()) {
      // Créer un nouveau document utilisateur
      await setDoc(userRef, {
        ...userData,
        createdAt: new Date(),
        role: 'user'
      });
      console.log('✅ Nouveau document utilisateur créé dans Firestore');
    } else {
      // Mettre à jour la dernière connexion
      await updateDoc(userRef, {
        lastLoginAt: new Date(),
        displayName: userData.displayName,
        photoURL: userData.photoURL
      });
      console.log('✅ Document utilisateur mis à jour dans Firestore');
    }
  } catch (error) {
    console.error('❌ Erreur lors de la création/mise à jour du document utilisateur:', error);
    throw error;
  }
};
```

### **3. Intégration dans signInWithGoogle**

**Ajout de l'appel :**
```typescript
// Créer ou mettre à jour le document utilisateur dans Firestore
await createOrUpdateUserDocument(user);
```

---

## **TESTS À EFFECTUER**

### **1. Test de Connexion**
- [ ] Connexion avec un nouvel utilisateur
- [ ] Vérification de la création du document Firestore
- [ ] Connexion avec un utilisateur existant
- [ ] Vérification de la mise à jour du `lastLoginAt`

### **2. Test des Permissions**
- [ ] Accès aux collections utilisateur après connexion
- [ ] Création de plantes
- [ ] Ajout de diagnostics
- [ ] Notifications

### **3. Test CORS**
- [ ] Popup Google Auth sans erreurs CORS
- [ ] Fermeture correcte de la popup
- [ ] Pas d'erreurs dans la console

---

## **MONITORING ET SUIVI**

### **Logs à Surveiller**
```javascript
// Succès de création utilisateur
console.log('✅ Nouveau document utilisateur créé dans Firestore');

// Succès de mise à jour
console.log('✅ Document utilisateur mis à jour dans Firestore');

// Erreurs potentielles
console.error('❌ Erreur lors de la création/mise à jour du document utilisateur:', error);
```

### **Métriques Netlify**
- Réduction des erreurs CORS
- Temps de chargement des pages
- Taux de réussite des connexions

---

## **PROCHAINES ÉTAPES**

1. **Déployer les corrections** sur Netlify
2. **Tester en production** avec différents comptes Google
3. **Surveiller les logs** pour s'assurer de l'absence d'erreurs
4. **Optimiser** si nécessaire les performances d'authentification

---

## **NOTES TECHNIQUES**

- La fonction `createOrUpdateUserDocument` est idempotente
- Elle gère automatiquement les nouveaux utilisateurs et les utilisateurs existants
- Les règles Firestore sont compatibles avec cette approche
- La politique CORS `unsafe-none` est nécessaire pour Firebase Auth avec popups
